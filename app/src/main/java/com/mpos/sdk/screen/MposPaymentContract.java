package com.mpos.sdk.screen;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.mpos.sdk.BasePresenter;
import com.mpos.sdk.BaseView;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataOfflinePay;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.modelma.McpInfo;
import com.mpos.sdk.core.modelma.PromotionInfo;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.mposinterface.ClickListeners;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.view.BottomSheetMultiCurrentcy;

import java.util.ArrayList;
import java.util.List;

public class MposPaymentContract {

    interface View extends BaseView<Presenter> {

        LibDspreadReader.ItfUpdateViewDspread getItfUpdateViewPr02();

        void cancelTimerCountDown();

        void showToast(String msg);
        void showError(String msg);
        void showError(DataError dataError);
        void showDialogAction(String msg, android.view.View.OnClickListener onclick);
        void showLoading(String msg, boolean show);
        void showDialogFailPay(MposPaymentModel.DataFailPay dataFailPay);
        void showDialogWaitSignature(DataReversalLogin dataReversal, ClickListeners onclick);
//        void showDialogWaitSignature(DataReversalLogin dataReversal, android.view.View.OnClickListener onclick);

        void onSuccessPay(DataPrePay dataPrePay, DataPay dataPay);
        void onSuccessVoid(DataPrePay dataPrePay, DataPay dataPay);
        void onSuccessPayOffline(DataPrePay dataPrePay, DataOfflinePay dataOfflinePay);

        <T> void onSuccessByAction(T t, MposTransactions.ActionTrans typeAction);

        void callbackToPartner(String status, DataError dataError, DataPrePay dataPrePay, DataPay dataPay);

        void requestLocationPermission();
        void requestEnableBlePermission();

        void showDialogEnableGps();

        void updateViewByStatePR(int state);

        void showViewMutilTrxPending(List<WfDetailRes> arr);

        void showDialogWarningUpgradeEmvConfig();
        void showDialogWarningUpgradeFw();

        void showProgressUpdateFw(int progress);

        void gotoScreenUpgradeFwSp02();

        void sendResultSuccessBeforeSignatureToEmartByBroadcast(DataPay dataPay);

        void handleActionTracking(String action, String msg);

//        void resetViewByPromotionInfo(ArrayList<PromotionInfo> promotionInfo);
        void resetAmountView();
        void showDialogMultiCurrency(ArrayList<McpInfo> arrMcp, BottomSheetMultiCurrentcy.ItfCallbackMcp itfCallbackMcp);
    }

    interface Presenter extends BasePresenter{

        int getCurrStageProcess();
        boolean isRunningUpgradeFwPr02();
        void processConnectDevice();
        void handleActivityResult(int requestCode, int resultCode, @Nullable Intent data);
        void onDestroyActivity();
        void processSignatureWhenResumeScreen();
        void processLogin(String serialNumber);

        void onSaveInstanceState(Bundle outState);

        void checkBeforePay();

        void downloadEmvConfig();
        void upgradeFirmware();
        void appendLog(ItfAppendLog.TypeLog type, String log);

        void saveNumSaleRemain(int num);

        void resetSaveInstanceState();
        boolean checkRestrictNfc();
        void handleActionFromBroadcast(String action, Intent intent);
    }


}
