package com.mpos.sdk.screen;

import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.dspread.xpos.QPOSService;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.UiCustomUtil;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataOfflinePay;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.modelma.*;
import com.mpos.sdk.core.mposinterface.ClickListeners;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.databinding.ActivityMposPaymentBinding;
import com.mpos.sdk.databinding.ActivityMposPaymentLiteBinding;
import com.mpos.sdk.databinding.DialogMposNtfBinding;
import com.mpos.sdk.databinding.VTopNotifyBinding;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.LanguageUtil;
import com.mpos.sdk.util.MyDialogShow;
import com.mpos.sdk.util.ScreenUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.util.UtilsSystem;
import com.mpos.sdk.view.BottomSheetMultiCurrentcy;
import com.mpos.sdk.view.DialogDetailWaitSignature;
import com.mpos.sdk.view.MposDialog;

import java.lang.ref.WeakReference;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.mpos.sdk.core.control.LibReaderController.UI_STAGE_CANCEL_SCAN_DEVICE;
import static com.mpos.sdk.core.control.MposIntegrationHelper.STATUS_UPDATED;
import static com.mpos.sdk.core.control.MposIntegrationHelper.TRANS_STATUS_CANCEL;
import static com.mpos.sdk.core.control.MposIntegrationHelper.TRANS_STATUS_ERROR;
import static com.mpos.sdk.screen.MposPaymentPresenter.RQ_CODE_ENABLE_GPS;
import static com.mpos.sdk.util.Intents.EXTRA_DATA_VERSION_LITE;


public class MposPaymentActivity extends AppCompatActivity implements MposPaymentContract.View {

    private final String TAG = this.getClass().getSimpleName();

    public static final int RESULT_NOT_HANDLER        	= 2;
    public static final int RESULT_FAILURE_PAY			= 3;

    List<View> layoutBluetooth;

    List<View> layoutDeviceConnected;

    List<View> layoutTimeRemaining;

    List<View> layoutWeAccept;

    List<View> layoutSingleAmount;

    List<View> layoutDoubleAmount;


    private long amount;

    private int currStageProcess = 0;
    private int currPercent;
    private boolean isShowEnableGps = false;
    private boolean versionLite = false;
    private boolean secondRun = false;
    private boolean hasBroadcastListenerAction = false;
    private String appType;

    private DataPrePay dataPrePay;
    private DataPay dataPayWaitSignature;
    private MposCustom sdkCustom;

    private AnimationHelper animationHelper;
    private MposPaymentContract.Presenter presenter;

    private ProgressDialog pgdl;
    private Timer timerPercent;
    private TimerTask scanTask;

    private int timeoutInputPin = 30;
    private Timer timerInputPin;
    private TimerTask timerTaskInputPin;
    private final Handler handler = new Handler();

    private MposIntegrationHelper mposIntegrationHelper;

    private MposPaymentModel payModel;

//    private Intent intentServiceLocation;
    private Intent intentBroadcastState;    // UI_STAGE
    private Intent intentBroadcastAction;   // action START/END transaction
    private Intent intentBroadcastTracking;   // tracking action

    ActivityMposPaymentBinding binding;
    ActivityMposPaymentLiteBinding bindingLite;
    VTopNotifyBinding bindingTopNotify;

    MultiViewHelper viewHelper;
    DataTracking dataTracking;

    Handler handlerHideViewPercent = new Handler();

    ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    protected void attachBaseContext(Context newBase) {
        LanguageUtil languageUtil = new LanguageUtil();
        super.attachBaseContext(languageUtil.changeLanguage(newBase, PrefLibTV.getInstance(newBase).getCustomLanguage()));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (DevicesUtil.isP20L() || DevicesUtil.isPax()) {
            versionLite = getIntent().getBooleanExtra(EXTRA_DATA_VERSION_LITE, false);
        }
        else if (DevicesUtil.isSP02()) {
            versionLite = true;
        }
        PrefLibTV.getInstance(this).put(PrefLibTV.versionLite, versionLite);

        Utils.LOGD(TAG, "onCreate: =====> versionLite="+versionLite);

        // need for find name of field
        binding = ActivityMposPaymentBinding.inflate(getLayoutInflater());
        if (versionLite) {
            bindingLite = ActivityMposPaymentLiteBinding.inflate(getLayoutInflater());
            bindingTopNotify = VTopNotifyBinding.inflate(getLayoutInflater(), bindingLite.getRoot(), true);
            setContentView(bindingLite.getRoot());
        }
        else {
            bindingTopNotify = VTopNotifyBinding.inflate(getLayoutInflater(), binding.getRoot(), true);
            setContentView(binding.getRoot());
        }

        viewHelper = new MultiViewHelper(versionLite, binding, bindingLite);
//        viewHelper.addFieldFromChild(binding.vNotify);
        init();

    }

    private void init() {

        initViewBinding();
        initData();
        boolean dataOk = validateData();

        initUi();

        MposPaymentPresenter presenter = new MposPaymentPresenter(this, this, payModel);
        presenter.setDataPrePay(dataPrePay);
        presenter.setDataPayWaitSignature(dataPayWaitSignature);
        setPresenter(presenter);

        setResult(RESULT_NOT_HANDLER);

        PrefLibTV.getInstance(this).setFlagDevices(dataPrePay.getReaderType().getReaderType());

        if (dataOk) {
            runPresenterInBackgroundThread();
        }
        if (!PrefLibTV.getInstance(this).getPermitSocket()) {
            executorService.execute(() -> SaveLogController.getInstance(MposPaymentActivity.this).pushLog(true));
        }
    }

    private void runPresenterInBackgroundThread() {
        Utils.LOGD(TAG, "startCallSdkPayment: timePay A--"+System.currentTimeMillis());
        myAsyncTask = new MyAsyncTask(new WeakReference<>(this));
        myAsyncTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    MyAsyncTask myAsyncTask;

    private static class MyAsyncTask extends AsyncTask<Void, Void, Void> {
        WeakReference<MposPaymentActivity> weakReference;

        public MyAsyncTask(WeakReference<MposPaymentActivity> weakReference) {
            this.weakReference = weakReference;
        }

        @Override
        protected Void doInBackground(Void... params) {

            return null;
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            super.onPostExecute(aVoid);
            if (weakReference != null && !weakReference.get().isDestroyed()) {
                weakReference.get().presenter.start();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        Utils.LOGD(TAG, "onPause: --->");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Utils.LOGD(TAG, "onResume: --->isShowEnableGps=" + isShowEnableGps + " secondRun=" + secondRun);
        isSaveInstanceState = false;
        if (isShowEnableGps) {
            isShowEnableGps = false;
            presenter.resetSaveInstanceState();
            presenter.checkBeforePay();
            return;
        }
        if (secondRun) {
            presenter.processSignatureWhenResumeScreen();
            if (waitShowDialogFail && dataFailPayCache != null) {
                waitShowDialogFail = false;
                showDialogFailPay(dataFailPayCache);
            }
        }
        secondRun = true;
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        Utils.LOGD(TAG, "onRestart: =====>");
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        Utils.LOGD(TAG, "onRestoreInstanceState: =====??");
    }

    boolean isSaveInstanceState = false;
    boolean waitShowDialogFail = false;
    MposPaymentModel.DataFailPay dataFailPayCache;
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        Utils.LOGD(TAG, "onSaveInstanceState: ===>>" + outState);
        isSaveInstanceState = true;
        presenter.onSaveInstanceState(outState);
    }

    private void initViewBinding() {

        layoutDeviceConnected = new ArrayList<>();
        layoutDeviceConnected.add(viewHelper.getViewByObj(binding.iconStatusConnect));
        layoutDeviceConnected.add(viewHelper.getViewByObj(binding.tvStatusConnect));
        layoutDeviceConnected.add(viewHelper.getViewByObj(binding.tvDeviceNameConnect));

        layoutBluetooth = new ArrayList<>();
        layoutTimeRemaining = new ArrayList<>();
        layoutWeAccept = new ArrayList<>();
        if (!versionLite) {
            layoutBluetooth.add(viewHelper.getViewByObj(binding.imvBluetooth));
            layoutBluetooth.add(viewHelper.getViewByObj(binding.tvReconnectReader));
            layoutBluetooth.add(viewHelper.getViewByObj(binding.insertCardTvOpenDevice));
            layoutBluetooth.add(viewHelper.getViewByObj(binding.progressBluetooth));

            layoutTimeRemaining.add(viewHelper.getViewByObj(binding.tvTitleTimeRemaining));
            layoutTimeRemaining.add(viewHelper.getViewByObj(binding.tvTimePin));
            layoutTimeRemaining.add(viewHelper.getViewByObj(binding.imvReaderInputPin));
            layoutTimeRemaining.add(viewHelper.getViewByObj(binding.tvGuidePin));
            layoutTimeRemaining.add(viewHelper.getViewByObj(binding.tvGuideCustomerPin));

            layoutWeAccept.add(viewHelper.getViewByObj(binding.insertCardSpace));
            layoutWeAccept.add(viewHelper.getViewByObj(binding.insertCardLabelBankPayment));
            layoutWeAccept.add(viewHelper.getViewByObj(binding.insertCardImgAcceptPayment));

            viewHelper.getViewByObj(binding.insertCardBtnCancel).setOnClickListener(view -> onClickBtnCancel());
            viewHelper.getViewByObj(binding.imvBluetooth).setOnClickListener(view -> onClickReconnectReader());
        }

        layoutSingleAmount = new ArrayList<>();
        layoutSingleAmount.add(viewHelper.getViewByObj(binding.tvAmountPay));
        layoutSingleAmount.add(viewHelper.getViewByObj(binding.insertCardTvCurrency));
        layoutSingleAmount.add(viewHelper.getViewByObj(binding.insertCardTvBillCode));

        layoutDoubleAmount = new ArrayList<>();
        layoutDoubleAmount.add(viewHelper.getViewByObj(binding.vBottomCenterHori));
        layoutDoubleAmount.add(viewHelper.getViewByObj(binding.insertCardTvAmountPayTitleAtm));
        layoutDoubleAmount.add(viewHelper.getViewByObj(binding.tvAmountAtm));
        layoutDoubleAmount.add(viewHelper.getViewByObj(binding.insertCardTvAmountPayTitleInter));
        layoutDoubleAmount.add(viewHelper.getViewByObj(binding.tvAmountInter));
    }

    private void initData() {
        payModel = new MposPaymentModel();

        dataPrePay = getIntent().getParcelableExtra(Intents.EXTRA_DATA_PRE_PAY);
        sdkCustom = (MposCustom) getIntent().getSerializableExtra(Intents.EXTRA_DATA_SDK_CUSTOM);
        String appType = getIntent().getStringExtra(Intents.EXTRA_DATA_APP_TYPE);

        UiCustomUtil.getInstance().setSdkCustom(sdkCustom);

        if (dataPrePay != null) {
            amount = dataPrePay.getAmount();
            PrefLibTV.getInstance(this).setUserId(dataPrePay.getMobileUser());

            if (TextUtils.isEmpty(dataPrePay.getMobilePass())) {
                dataPrePay.setMobilePass(ConstantsPay.P_DEFAULT);
            }
        }
        mposIntegrationHelper = new MposIntegrationHelper();
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            dataPayWaitSignature = (DataPay) getIntent().getSerializableExtra(Intents.EXTRA_DATA_PAY_MP);
            if (dataPayWaitSignature != null && !TextUtils.isEmpty(dataPayWaitSignature.getUdid())) {
                if (dataPrePay == null) {
                    dataPrePay = new DataPrePay();
                }
                dataPrePay.setActionType(MposIntegrationHelper.A_CONTINUE_TRANS);
                dataPrePay.setUdid(dataPayWaitSignature.getUdid());
            }
            if (dataPrePay != null) {
                dataPrePay.setAppType(appType);
                dataPrePay.setbName(getIntent().getStringExtra(Intents.EXTRA_DATA_BNAME));
            }

            Bundle bundleExtra = getIntent().getBundleExtra(Intents.EXTRA_DATA_EXTRA_PARAMS);
            payModel.setBundleExtra(bundleExtra);
            if (getIntent().hasExtra(Intents.EXTRA_DATA_CARD_TRADE_MODE)) {
                payModel.setCardTradeModePr02(
                        (QPOSService.CardTradeMode) getIntent().getSerializableExtra(Intents.EXTRA_DATA_CARD_TRADE_MODE));
            }

            payModel.setSkipWaitSignature(getIntent().getBooleanExtra(Intents.EXTRA_DATA_SKIP_WAIT_SIGNATURE, false));

        }

        Utils.LOGD(TAG, "initData: dataPrePay.getHandlerStatePartner()="
                + (dataPrePay == null ? "" : dataPrePay.getHandlerStatePartner()));
        if (dataPrePay != null && !TextUtils.isEmpty((dataPrePay.getHandlerStatePartner()))) {
            intentBroadcastState = new Intent(dataPrePay.getHandlerStatePartner());
        }
        if (dataPrePay != null && !TextUtils.isEmpty(dataPrePay.getHandlerTrackingActionPay())) {
            intentBroadcastTracking = new Intent(dataPrePay.getHandlerTrackingActionPay());
        }
        Utils.LOGD(TAG, "initData: dataPrePay.getHandlerActionPayment()="
                + (dataPrePay == null ? "" : dataPrePay.getHandlerActionPayment()));
        if (PrefLibTV.getInstance(this).getPermitSocket() && (dataPrePay != null) && !TextUtils.isEmpty((dataPrePay.getHandlerActionPayment()))) {
            hasBroadcastListenerAction = true;
            sendActionToEmartByBroadcast(Constants.AP_ACTION_START);
        }
        if ((PrefLibTV.getInstance(this).getPermitSocket() || PrefLibTV.getInstance(this).getEnableReceiverCancelOrder())
                && (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
            registerReceiverCancelOrder();
        }
        registerReceiverHandleAction();
    }

    private boolean validateData() {
        if (dataPrePay == null) {
            showError(getString(R.string.mp_error_not_found_dataPrePay));
            return false;
        }
        else if (TextUtils.isEmpty(PrefLibTV.getInstance(this).getUserId())) {
            showError(getString(R.string.mp_error_not_found_user_pay));
            return false;
        }
        else {
            // check type of action
            // continue transaction by udid
            if (MposIntegrationHelper.A_CONTINUE_TRANS.equals(dataPrePay.getActionType())) {
                if (TextUtils.isEmpty(dataPrePay.getUdid())) {
                    showError(getString(R.string.error_udid_empty));
                    return false;
                }
                return true;
            }
            // check trans wait signature
            else if (MposIntegrationHelper.A_CHECK_UNSIGNATURE_TRANS.equals(dataPrePay.getActionType())) {

                return true;
            }
            // get status trans
            else if (MposIntegrationHelper.A_GET_STATUS.equals(dataPrePay.getActionType())) {

                return true;
            }
            // get list wait signature
            else if (MposIntegrationHelper.A_GET_LIST_WAIT_SIGN.equals(dataPrePay.getActionType())) {

                return true;
            }
            // update config + fw
            else if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {

                return true;
            }
            // check for action void
            else if (MposIntegrationHelper.A_VOID_TRANS.equals(dataPrePay.getActionType())) {
                if (TextUtils.isEmpty(dataPrePay.getTxId())) {
                    showError(getString(R.string.error_txid_empty));
                    return false;
                }
                return true;
            }
            // default: charge
            else {
                if (amount <= 0) {
                    showError(getString(R.string.error_wrong_amount));
                    return false;
                }
                else {
                    viewHelper.setText(binding.tvAmountPay, getAmountFormatted(amount, false));
                    return true;
                }
            }
        }
    }

    private void initUi() {
        Utils.LOGD(TAG, "initUi: ");

        if (sdkCustom!=null && sdkCustom.isLandscape()) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }

        if (versionLite && DevicesUtil.isPax()) {
            showHideViews(layoutDeviceConnected, false);
        }

        if (sdkCustom!=null) {
            if (sdkCustom.isShowToolbar()) {
                setupCustomStatusBar();
                if (versionLite) {
                    viewHelper.setText(binding.toolbarTitle,"");
//                if (binding.icBackLite != null) {
                    viewHelper.getViewByObj(binding.icBackLite).setVisibility(View.VISIBLE);
                    viewHelper.getViewByObj(binding.icBackLite).setOnClickListener(view -> onBackPressed());
//                }
                }
                else {
                    viewHelper.setText(binding.toolbarTitle,getString(R.string.mp_swipe_card));
                    if (!TextUtils.isEmpty(sdkCustom.getColorStatusBar())) {
                        try {
                            viewHelper.getViewByObj(binding.vToolbar).setBackgroundColor(Color.parseColor(sdkCustom.getColorStatusBar()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                        if (sdkCustom.getBtnBackStatusBarId() > 0) {
                            viewHelper.getViewByObj(binding.vToolbar).setNavigationIcon(sdkCustom.getBtnBackStatusBarId());    // support in 24.0.0
                        }
                        else {
                            viewHelper.getViewByObj(binding.vToolbar).setNavigationIcon(R.drawable.ic_back);    // support in 24.0.0
                        }
                        viewHelper.getViewByObj(binding.vToolbar).setNavigationOnClickListener(v -> {
                            Utils.LOGD("ViewToolBar", "onclick navigation---------");
                            onBackPressed();
                        });
                    }
                }
            }
            else {
                viewHelper.getViewByObj(binding.vToolbar).setVisibility(View.GONE);
            }
        }
        if (dataPrePay != null && dataPrePay.getAmountDomestic() > 0 && dataPrePay.getAmountInternational() > 0) {
            showHideViews(layoutSingleAmount, false);
            showHideViews(layoutDoubleAmount, true);
            viewHelper.setText(binding.tvAmountAtm,getAmountFormatted(dataPrePay.getAmountDomestic()));
            viewHelper.setText(binding.tvAmountInter,getAmountFormatted(dataPrePay.getAmountInternational()));
        }

        initTextSwitcher();

        initAnimationGuideSwipeCard();

        if (sdkCustom != null) {
            changeStyleByCustom();
        }
        viewHelper.setText(binding.insertCardTvCurrency,getCurrencyShow());
    }

    private void initAnimationGuideSwipeCard() {
        if (animationHelper == null) {
            animationHelper = new AnimationHelper(this, dataPrePay.getReaderType(), viewHelper.getViewByObj(binding.vAnim), versionLite);
        }
    }

    private void setupCustomStatusBar() {
        if (!TextUtils.isEmpty(sdkCustom.getColorStatusBar())) {
            Window window = getWindow();

            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);

            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.parseColor(sdkCustom.getColorStatusBar()));
        }
    }

//    public void resetViewByPromotionInfo(ArrayList<PromotionInfo> promotionInfo){
//        amount = dataPrePay.getAmount();
//        viewHelper.setText(binding.tvAmountPay, getAmountFormatted(amount, false));
//    }

    @Override
    public void resetAmountView() {
        amount = dataPrePay.getAmount();
        viewHelper.setText(binding.tvAmountPay, getAmountFormatted(amount, false));
    }


    @Override
    public void showDialogMultiCurrency(ArrayList<McpInfo> mcpInfos, BottomSheetMultiCurrentcy.ItfCallbackMcp itfCallbackMcp) {
//            updateViewByStage(UI_STATE_SELECT_PROMOTION);
//            currStageProcess = UI_STATE_SELECT_PROMOTION;
        BottomSheetMultiCurrentcy dialogMcp = BottomSheetMultiCurrentcy.newInstance(mcpInfos, dataPrePay.getAmount());
        dialogMcp.setCallback(itfCallbackMcp);
        dialogMcp.setCancelable(false);
        dialogMcp.show(getSupportFragmentManager(), dialogMcp.getTag());
    }


    String getAmountFormatted(long amount) {
        return getAmountFormatted(amount, true);
    }
    String getAmountFormatted(long amount, boolean includeCurrency) {

        String charSplitAmount = sdkCustom == null ? Constants.AMOUNT_SPLIT : sdkCustom.getCurrencyFormat();
        Utils.LOGD(TAG, "getAmountFormatted: --charSplitAmount=" + charSplitAmount);
        return String.format("%s%s", Utils.zenMoney(amount, charSplitAmount), includeCurrency ? getCurrencyShow() : "");
    }

    String getCurrencyShow() {
        String currencyShow = ConstantsPay.CURRENCY_SPACE_PRE;
        if (sdkCustom != null && !TextUtils.isEmpty(sdkCustom.getCurrencyShow())) {
            currencyShow = sdkCustom.getCurrencyShow();
        }
        return currencyShow;
    }

    private void initTextSwitcher() {
        changeFactoryTextSwitcher();
        viewHelper.getViewByObj(binding.tsStage).setInAnimation(AnimationUtils.loadAnimation(this, R.anim.push_up_in));
        viewHelper.getViewByObj(binding.tsStage).setOutAnimation(this, R.anim.push_up_out);
        setTextByStage(getString(R.string.msg_guide_connect_reader));
    }

    private void changeFactoryTextSwitcher() {
        viewHelper.getViewByObj(binding.tsStage).removeAllViews();
        viewHelper.getViewByObj(binding.tsStage).setFactory(() -> {
            TextView myText = new TextView(this);
            myText.setGravity(Gravity.CENTER);

            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ActionBar.LayoutParams.WRAP_CONTENT, ActionBar.LayoutParams.WRAP_CONTENT, Gravity.CENTER);
            myText.setLayoutParams(params);
            myText.setMinLines(2);
            myText.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimensionPixelSize(R.dimen.text_size_large));

            myText.setTextColor(versionLite ? Color.BLACK : Color.WHITE);

            return myText;
        });
    }

    private void changeStyleByCustom() {
        if (sdkCustom != null) {
            mposIntegrationHelper.changeBackgroundColor(viewHelper.getViewByObj(binding.getRoot()), sdkCustom);
            mposIntegrationHelper.changeBackgroundColor(getViewProgress(), sdkCustom);
//            mposIntegrationHelper.changeBackgroundColor(viewHelper.getViewByObj(binding.vProgress), sdkCustom);
            mposIntegrationHelper.changeNavigationColor(getWindow(), sdkCustom);
        }
    }

    private View getViewProgress() {
        return versionLite?bindingLite.vProgress:binding.vProgress;
//        return versionLite?bindingLite.vProgress.getRoot():binding.vProgress;
    }

    @Override
    public void requestLocationPermission() {
        Utils.LOGW(TAG, "requestLocationPermission: ");
        UtilsSystem.requestLocationPermission(this, MposPaymentPresenter.RQ_CODE_PERMISSION_LOCATION);
    }

    @Override
    public void requestEnableBlePermission() {
        UtilsSystem.requestBluetoothPermission(this, MposPaymentPresenter.RQ_CODE_PERMISSION_BLE);
    }

    @Override
    public void showDialogEnableGps() {
        Utils.LOGD(TAG, "showDialogEnableGps: ");
        if (DevicesUtil.isP20L() && GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this) == ConnectionResult.SUCCESS) {
            UtilsSystem.requestFusedLocation(this, RQ_CODE_ENABLE_GPS,
                    () -> AppExecutors.getInstance().mainThread().execute(this::buildAlertMsgNoGps));
        }
        else {
            buildAlertMsgNoGps();
        }
    }

    private void buildAlertMsgNoGps() {
        Utils.LOGD(TAG, "buildAlertMsgNoGps: ---->");
        UtilsSystem.buildAlertMessageNoGps(this, getString(R.string.ALERT_LOCATION_SERVICE_ANDROID_MSG), RQ_CODE_ENABLE_GPS,
                (dialogInterface, i) -> isShowEnableGps = true,
                (dialogInterface, i) -> callbackCancel());
    }

    protected void onClickBtnCancel() {
        exitPayment();
    }

    protected void onClickReconnectReader() {
        Utils.LOGD(TAG, "onClickReconnectReader: CurrStageUi="+ payModel.getCurrStageUi());
        if (payModel.getCurrStageUi() == UI_STAGE_CANCEL_SCAN_DEVICE) {
            presenter.processConnectDevice();
        }
    }

    @Override
    public LibDspreadReader.ItfUpdateViewDspread getItfUpdateViewPr02() {
        return itfUpdateViewDspread;
    }

    private void showUiWaitSwipeCard() {
        String msgShow = "";
        boolean restrictNfc = presenter.checkRestrictNfc();
        if (restrictNfc) {
            msgShow = getString(versionLite ? R.string.mp_tv_guide_payment_card_lite_no_nfc : R.string.mp_tv_guide_payment_card_no_nfc);
        }
        if (TextUtils.isEmpty(msgShow)) {
            msgShow = getString(versionLite ? R.string.mp_tv_guide_payment_card_lite : R.string.mp_tv_guide_payment_card);
        }
        updateUIConnectDevice(STATUS_CONNECTED);
        updateIUStatusConnectDevice(true);
        setTextByStage(msgShow);
        handler.postDelayed(() -> {
            initAnimationGuideSwipeCard();
            if (animationHelper != null) {
                animationHelper.runAnimGuideMpos();
            }
        }, 800);
    }

    LibDspreadReader.ItfUpdateViewDspread itfUpdateViewDspread = new LibDspreadReader.ItfUpdateViewDspread() {
        @Override
        public void showViewDspreadByStage(int stage) {
            updateViewByStatePR(stage);
        }

        @Override
        public void showDialogErrorWithMessage(DataError dataError) {
            Utils.LOGD(TAG, "showDialogErrorWithMessage: type=" + dataError.getErrorCode() + " msg=" + dataError.getMsg());
            String msgError = getString(R.string.error_format, dataError.getMsg(), dataError.getErrorCode());
            showError(msgError);
        }

        @Override
        public void showNotify(DataNotifyPay dataNotifyPay) {
            showNotificationPayment(dataNotifyPay);
        }

        @Override
        public void showDialogFallbackDspread(String s) {
//            MyDialogShow.showDialogRetry(getString(R2.string.FALLBACK_NOTI_SWIPE_CARD),
//                    MposPaymentActivity.this, v -> setupDspreadReader());
        }

        @Override
        public void onSuccessConnectDeviceDspread(String s, String s1) {

        }
    };

    // update view by state
    @Override
    public void updateViewByStatePR(final int state) {

        sendStateToPartner(state);

        runOnUiThread(() -> {
            Utils.LOGD(TAG, "updateViewByStatePR: state=" + state);
            switch (state) {
                case LibReaderController.UI_STAGE_SHOW_PROGRESS:
                    updateIUStatusConnectDevice(false);
                    updateUIConnectDevice(STATUS_CONNECTING);
                    break;
                case LibReaderController.UI_STAGE_HIDE_PROGRESS:
                    updateIUStatusConnectDevice(false);
                    break;
                case LibReaderController.UI_STAGE_FALSE_CONNECT_DEVICE:
                    updateUIConnectDevice(STATUS_RETRY_CONNECT);
                    updateIUStatusConnectDevice(false);
                    setTextByStage(getString(R.string.msg_guide_connect_reader));
                    payModel.setCurrStageUi(UI_STAGE_CANCEL_SCAN_DEVICE);
                    break;
                case LibReaderController.UI_STAGE_NO_ENABLE_BLUETOOTH:
                    setTextByStage(getString(R.string.MSG_ENABLE_BLUETOOTH));
                    break;
                case LibReaderController.UI_STAGE_NO_CONNECT_DEVICE:
                    updateUIConnectDevice(STATUS_CONNECTING);
                    updateIUStatusConnectDevice(false);
                    setTextByStage(getString(R.string.DEVICE_NOT_CONNECT));
                    if (presenter.isRunningUpgradeFwPr02()) {
                        handlerHideViewPercent.postDelayed(this::hideViewPercent, 3000);
                    }
                    else {
                        hideViewPercent();
                    }
                    break;
                case LibReaderController.UI_STAGE_CONNECTING_DEVICE:
                    updateUIConnectDevice(STATUS_CONNECTING);
                    updateIUStatusConnectDevice(false);
                    break;
                case LibReaderController.UI_STAGE_CONNECTED_DEVICE:
                    updateUIConnectDevice(STATUS_CONNECTED);
                    updateIUStatusConnectDevice(true);
                    break;
                case LibReaderController.UI_STAGE_WAIT_SWIPE_CARD:
                    hideViewPercent();
                    showUiWaitSwipeCard();
                    break;
                case LibReaderController.UI_STAGE_PROCESSING_CARD:
                    setTextByStage(getString(R.string.msg_processing_card));
                    viewHelper.setText(binding.tvStage,getString(R.string.transaction_processing));
                    viewHelper.setText(binding.tvDetailProgress,getString(R.string.warning_close_app_turn_off_reader));
                    showViewPercent();
                    clearAnimation();
                    currPercent = 0;
                    startCountPercentProcessing();
                    if (DevicesUtil.isPax()) {
                        hideViewPreInputPinLite(View.GONE);
                    }
                    handleActionTracking(DataTracking.ActionTracker.read_card, "");
                    break;
                case LibReaderController.UI_STAGE_ENTER_PIN:
                    if (DevicesUtil.isPax()) {
                        hideViewPreInputPinLite(View.VISIBLE);
                    } else {
                        hideViewPreInputPin();
                    }
                    setTextByStage(getString(R.string.ENTER_PIN));
                    clearAnimation();

                    hideViewPercent();
                    showViewTimeRemainingInputPin();
                    break;
                case LibReaderController.UI_STAGE_SELECT_APPLICATION:
                    setTextByStage(getString(R.string.select_application_and_guide));
                    break;
                case LibReaderController.UI_STAGE_SENDING_DATA:
                    showUiSendingData();
                    break;
                case LibReaderController.UI_STAGE_GET_MERCHANT_INFO:
                    setTextByStage(getString(R.string.mp_stage_load_merchant_info));
                    break;
                case LibReaderController.UI_STAGE_START_UPDATE_FW:
                    showUiStartUpdateFw();
                    break;
//                case LibReaderController.UI_STAGE_END_SCAN_DEVICE:
//                    setTextByStage(getString(R.string.mp_stage_remote_card));
//                    break;
                default:
                    break;
            }
        });
    }

    private void showUiStartUpdateFw() {
        viewHelper.getViewByObj(binding.pgbProcess).setVisibility(View.VISIBLE);
        viewHelper.getViewByObj(binding.tvPercent).setVisibility(View.VISIBLE);
        viewHelper.setText(binding.tvStage,getString(R.string.msg_upgrading_fw));
        viewHelper.setText(binding.tvDetailProgress,getString(R.string.warning_upgrading_fw));
        showViewPercent();
    }

    private void showNotificationPayment(DataNotifyPay dataNotify) {
        Utils.LOGD(TAG, "showNotificationPayment: -->" + dataNotify.getMessage());

        Animation animation = AnimationUtils.loadAnimation(getApplicationContext(),
                R.anim.slide_top_to_bottom_in);
        TextView tvNotify = bindingTopNotify.tvNotify;//.setVisibility(View.VISIBLE);
        tvNotify.setText(dataNotify.getMessage());
        tvNotify.startAnimation(animation);
        tvNotify.setVisibility(View.VISIBLE);

        new Handler().postDelayed(() -> {
            try {
                Animation animationOut = AnimationUtils.loadAnimation(getApplicationContext(),
                        R.anim.slide_top_to_bottom_out);

                tvNotify.startAnimation(animationOut);
                tvNotify.setVisibility(View.GONE);
            } catch (Exception e) {
                Utils.LOGD(TAG, "run: " + e.getMessage());
            }
        }, 4000);
    }

    @Override
    public void showProgressUpdateFw(int progress) {
        handlerHideViewPercent.removeCallbacksAndMessages(null);
        handler.post(()->{
            showViewPercent();
            setTextPercent(progress);
        });
    }

    private void sendStateToPartner(int stage) {
        if (intentBroadcastState != null) {
            intentBroadcastState.putExtra("stage", stage);
            sendBroadcast(intentBroadcastState);
        }
    }

    @Override
    public void handleActionTracking(String action, String msg) {
        if (dataTracking == null) {
            dataTracking = new DataTracking("SCREEN_CARD_PAYMENT");
        }
        dataTracking.setAction(action);
        dataTracking.setMessage(msg);
        sendActionToTracker(dataTracking);
    }

    protected void handleActionTracking(DataTracking.ActionTracker action, String msg) {
        handleActionTracking(action.toString(), msg);
    }

    protected void sendActionToTracker(DataTracking dataTracking) {
        if (intentBroadcastTracking != null && dataTracking != null) {
            intentBroadcastTracking.putExtra(Intents.EXTRA_DATA_TRACKING, dataTracking);
            sendBroadcast(intentBroadcastTracking);
        }
    }

    @Override
    public void sendResultSuccessBeforeSignatureToEmartByBroadcast(DataPay dataPay) {
        Utils.LOGD(TAG, "sendResultSuccessToEmart: hasListener=" + hasBroadcastListenerAction);
        if (hasBroadcastListenerAction) {
            initIntentBroadcastAction();
            String result = mposIntegrationHelper.buildDataSuccess(dataPrePay, dataPay);
            Utils.LOGD(TAG, "sendResultSuccessToEmart: data=" + result);
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, Constants.AP_ACTION_BEFORE_SIGNATURE);
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_RESULT, result);
            sendBroadcast(intentBroadcastAction);
        }
    }

    private void sendResultToEmartByBroadcast(String msgResult) {
        if (hasBroadcastListenerAction) {
            initIntentBroadcastAction();
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, Constants.AP_ACTION_CALLBACK_DATA);
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_RESULT, msgResult);
            Utils.LOGD(TAG, "sendResultToEmartByBroadcast " + dataPrePay.getHandlerActionPayment());
            sendBroadcast(intentBroadcastAction);
        }
    }

    private void sendResultCancelOrderToEmartByBroadcast(String msgResult, boolean isCancel) {
        if (hasBroadcastListenerAction) {
            initIntentBroadcastAction();
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, Constants.AP_ACTION_CANCEL_ORDER);
            if (isCancel) {
                intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, true);
            } else {
                intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, false);
            }
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_RESULT, msgResult);
            Utils.LOGD(TAG, "sendResultToEmartByBroadcast " + dataPrePay.getHandlerActionPayment());
            sendBroadcast(intentBroadcastAction);
        }
    }

    private void sendActionToEmartByBroadcast(String action) {
        if (hasBroadcastListenerAction) {
            initIntentBroadcastAction();
            intentBroadcastAction.putExtra(Intents.EXTRA_DATA_BC_ACTION, action);
            sendBroadcast(intentBroadcastAction);
        }
    }

    private void initIntentBroadcastAction() {
        intentBroadcastAction = new Intent(dataPrePay.getHandlerActionPayment());
    }

    private void showUiSendingData() {
        setTextByStage(getString(R.string.transaction_processing));
        Utils.LOGD(TAG, "updateViewByStagePR: UI_STAGE_SENDING_DATA---" + presenter.getCurrStageProcess());
        if (presenter.getCurrStageProcess() == LibReaderController.STAGE_PROCESS_MAGSTRIPE_PIN
                || presenter.getCurrStageProcess() == LibReaderController.STAGE_PROCESS_EMV_CONFIRM
                || presenter.getCurrStageProcess() == LibReaderController.STAGE_PROCESS_START_EMV
        ) {
            cancelTimerInputPin();
        }
        showViewPercent();
        clearAnimation();
        if (!versionLite) {
            startCountPercentProcessing();
        }
    }

    private void showViewPercent() {
        if (!versionLite && viewHelper.getViewByObj(binding.vDetailPayment) != null) {
            viewHelper.getViewByObj(binding.vDetailPayment).setVisibility(View.GONE);
        }
        getViewProgress().setVisibility(View.VISIBLE);
//        viewHelper.getViewByObj(binding.vProgress).setVisibility(View.VISIBLE);
    }

    private void hideViewPercent() {
        if (!versionLite && viewHelper.getViewByObj(binding.vDetailPayment) != null) {
            viewHelper.getViewByObj(binding.vDetailPayment).setVisibility(View.VISIBLE);
        }
        getViewProgress().setVisibility(View.GONE);
//        viewHelper.getViewByObj(binding.vProgress).setVisibility(View.GONE);
    }

    private void clearAnimation() {
        if (animationHelper != null) {
            animationHelper.clearAnimation();
        }
        animationHelper = null;
    }

    private void updateIUStatusConnectDevice(boolean isConnected) {
        if (isConnected) {
            viewHelper.getViewByObj(binding.iconStatusConnect).setImageResource(R.drawable.ic_checked_green);
            viewHelper.getViewByObj(binding.tvStatusConnect).setTextColor(ContextCompat.getColor(this, R.color.black_light));
            if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP01
                    || dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP02
            ) {
                viewHelper.setText(binding.tvStatusConnect,getString(R.string.sdk_serial_number));
            }
            else {
                viewHelper.setText(binding.tvStatusConnect,getString(R.string.connected_device));
            }
        } else {
            viewHelper.getViewByObj(binding.iconStatusConnect).setImageResource(R.drawable.ic_checked_red);
            viewHelper.getViewByObj(binding.tvStatusConnect).setTextColor(ContextCompat.getColor(this, R.color.red));
            viewHelper.setText(binding.tvStatusConnect,getString(R.string.check_connect_device));
        }
        handleActionTracking(DataTracking.ActionTracker.connect_device, payModel.getSerialnumber());
        viewHelper.setText(binding.tvDeviceNameConnect, payModel.getSerialnumber());
    }

    private void hideViewPreInputPin() {
        viewHelper.getViewByObj(binding.vDetailPayment).setVisibility(View.VISIBLE);
        viewHelper.getViewByObj(binding.vAnim).setVisibility(View.GONE);
        viewHelper.getViewByObj(binding.vBgAmount).setVisibility(View.GONE);
        showHideViews(layoutWeAccept, false);
        showHideViews(layoutSingleAmount, false);
        showHideViews(layoutDoubleAmount, false);
        showHideViews(layoutDeviceConnected, false);
    }

    private void hideViewPreInputPinLite(int visibility) {
//        viewHelper.getViewByObj(binding.vBlur).setVisibility(View.VISIBLE);
        viewHelper.getViewByObj(binding.vBlur).setVisibility(visibility);
    }

    private final String STATUS_CONNECTED       = "CONNECTED";
    private final String STATUS_CONNECTING      = "CONNECTING";
    private final String STATUS_RETRY_CONNECT   = "RETRYCONNECT";
    private void updateUIConnectDevice(String status) {
        switch (status) {
            case STATUS_CONNECTED:
                viewHelper.getViewByObj(binding.vAnim).setVisibility(View.VISIBLE);
                goneViewGuideBluetooth();
                break;
            case STATUS_CONNECTING:
                showHideViews(layoutBluetooth, true);
                viewHelper.getViewByObj(binding.layoutGuide).setVisibility(View.VISIBLE);
                viewHelper.getViewByObj(binding.vAnim).setVisibility(View.GONE);
                viewHelper.getViewByObj(binding.imvBluetooth).setImageResource(R.drawable.ic_bluetooth_waiting_connect);
                viewHelper.getViewByObj(binding.progressBluetooth).setVisibility(View.VISIBLE);
                viewHelper.setText(binding.tvReconnectReader,getString(R.string.retry_connect));
                viewHelper.getViewByObj(binding.tvReconnectReader).setTextColor(ContextCompat.getColor(this, R.color.orange));
                break;
            case STATUS_RETRY_CONNECT:
                showHideViews(layoutBluetooth, true);
                viewHelper.getViewByObj(binding.layoutGuide).setVisibility(View.VISIBLE);
                viewHelper.getViewByObj(binding.vAnim).setVisibility(View.GONE);
                viewHelper.getViewByObj(binding.imvBluetooth).setImageResource(R.drawable.ic_bluetooth_cennect);
                viewHelper.getViewByObj(binding.progressBluetooth).setVisibility(View.GONE);
                viewHelper.setText(binding.tvReconnectReader,getString(R.string.retryconnect_device));
                viewHelper.getViewByObj(binding.tvReconnectReader).setTextColor(ContextCompat.getColor(this, R.color.mp_blue));
                break;
        }
    }

    private void goneViewGuideBluetooth() {
        showHideViews(layoutBluetooth, false);

        if (!versionLite && viewHelper.getViewByObj(binding.layoutGuide) != null) {
            viewHelper.getViewByObj(binding.layoutGuide).setVisibility(View.GONE);
        }
    }

    private void showHideViews(List<View> views, boolean show) {
        for (View view : views) {
            if (view != null) {
                view.setVisibility(show ? View.VISIBLE : View.GONE);
            }
        }
    }

    private void hideLoading() {
        try {
            if(pgdl!=null && pgdl.isShowing() && !isDestroyed()) pgdl.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showLoading(String msg) {
        if(isDestroyed()){
            return;
        }
        if (pgdl == null) {
            pgdl = new ProgressDialog(this);
            pgdl.setCancelable(false);
        }
        if (TextUtils.isEmpty(msg)) {
            pgdl.setMessage(getString(R.string.txt_loading));
        }
        else {
            pgdl.setMessage(msg);
        }
        pgdl.show();
    }

    private void setTextByStage(String textByStage) {
        Utils.LOGD(TAG, "setTextByStage: "+textByStage);
        viewHelper.getViewByObj(binding.tsStage).setText(textByStage);
    }

    public void startCountPercentProcessing() {
        try {
            runOnUiThread(() -> {

                viewHelper.getViewByObj(binding.tvPercent).setVisibility(View.VISIBLE);
                setTextPercent(currPercent);
                cancelTimerCountDown();

                scanTask = new TimerTask() {
                    @Override
                    public void run() {
                        handler.post(() -> {
//                            Utils.LOGD(TAG, "showPercentProcessing: -------");
                            int percent = getPercentByStageProcess();
                            setTextPercent(percent);
                        });
                    }
                };
                timerPercent = new Timer();
                timerPercent.schedule(scanTask, 0, 2000);
            });
        } catch (Exception e) {
            try {
                runOnUiThread(() -> viewHelper.setText(binding.tvPercent,getString(R.string.msg_please_wait)));
            } catch (Exception e1) {
                Utils.LOGE(TAG, "showPercentProcessing please wait: ", e1);
            }
        }
    }

    private void setTextPercent(int percent) {
        viewHelper.setText(binding.tvPercent,MessageFormat.format("{0}%", percent));
        viewHelper.getViewByObj(binding.pgbProcess).setProgress(percent);
    }

    private int getPercentByStageProcess() {
        boolean canIncrease = false;

        currStageProcess = presenter==null? 0 : presenter.getCurrStageProcess();
//        Utils.LOGD(TAG, "getPercentByStageProcess: currStageProcess=" + currStageProcess);
        if (currStageProcess == LibReaderController.STAGE_SIGNATURE
                || currStageProcess == LibReaderController.STAGE_PROCESS_END) {
            cancelTimerCountDown();
        }
        else if ( ( (currStageProcess == LibReaderController.STAGE_PROCESS_START_MAGSTRIPE
                    || currStageProcess == LibReaderController.STAGE_PROCESS_START_EMV
                    || currStageProcess == LibReaderController.STAGE_PROCESS_START)
                    && currPercent < 25)
                || ((currStageProcess == LibReaderController.STAGE_PROCESS_MAGSTRIPE_SALE
                    || currStageProcess == LibReaderController.STAGE_PROCESS_EMV_SALE)
                    && currPercent < 40)
                || (currStageProcess == LibReaderController.STAGE_PROCESS_EMV_RUN_SCRIPT
                    && currPercent < 60)
                || ((currStageProcess == LibReaderController.STAGE_PROCESS_MAGSTRIPE_PIN
                    || currPercent == LibReaderController.STAGE_PROCESS_EMV_CONFIRM)
                    && currPercent < 80)
        ) {
            canIncrease = true;
        }
        if (canIncrease) {
            currPercent += 5;
        }
        return currPercent;
    }

    private void showViewTimeRemainingInputPin() {
        Utils.LOGD(TAG, "showViewTimeRemainingInputPin: ====>>");
        showHideViews(layoutTimeRemaining, true);

        cancelTimerInputPin();

        timerTaskInputPin = new TimerTask() {
            @Override
            public void run() {
                if (timeoutInputPin < 0) {
                    cancelTimerInputPin();
                }
                else {
                    handler.post(() -> showTimeRemaining(timeoutInputPin--));
                }
            }
        };

        timerInputPin = new Timer();
        timerInputPin.schedule(timerTaskInputPin, 0, 1000);
    }

    private void showTimeRemaining(long time) {
        Utils.LOGD(TAG, "showTimeRemaining: time="+time);
        String timeRemaining = DateUtils.formatElapsedTime(time);
        viewHelper.setText(binding.tvTimePin,timeRemaining);
    }

    private void cancelTimerInputPin() {
        Utils.LOGD(TAG, "cancelTimerInputPin: -----------");
        if (timerInputPin != null) {
            timerInputPin.cancel();
            timerInputPin = null;
        }
        if (timerTaskInputPin != null) {
            timerTaskInputPin.cancel();
            timerTaskInputPin = null;
        }
    }

    @Override
    public void showToast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_LONG).show();
    }

    @Override
    public void showError(String msg) {
        DataError dataError = new DataError(ConstantsPay.ERROR_CODE_SDK_DEFAULT, msg);
        showError(dataError);
    }

    public void showError(int code, String msg) {
        DataError dataError = new DataError(code, msg);
        showError(dataError);
    }

    @Override
    public void showError(@NonNull DataError dataError) {
        if (ConstantsPay.ERROR_CODE_NOT_FOUND == dataError.getErrorCode()) {
            callbackNotFoundUnsign();
        }
        else {
            String title;
            if (dataError.getErrorCode() > 0) {
                title = getString(R.string.mp_dialog_error_title, dataError.getErrorCode());
            }
            else {
                title = getString(R.string.dialog_error_title_default);
            }
            MyDialogShow.showDialogError(title, dataError.getMsg(), this, false, view -> callbackFail(null, dataError));
        }
    }

    @Override
    public void showLoading(String msg, boolean show) {
        if (show) {
            showLoading(msg);
        }
        else {
            hideLoading();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        appendLog("* onStop-SDK *");
    }

    @Override
    public void onDestroy() {
//        isDestroy = true;
        appendLog("* onDestroy-SDK *");

        hideLoading();

        super.onDestroy();
        try {
            payModel.setDestroyActivity(true);
            cancelTimerInputPin();
            cancelTimerCountDown();
            clearAnimation();
            if (presenter != null) {
                presenter.onDestroyActivity();
            }
            unregisterReceiver();

            if (PrefLibTV.getInstance(this).getPermitSocket() && !TextUtils.isEmpty(resultToEmart)) {
//                sendResultToEmartByBroadcast(resultToEmart);
                sendResultToEmartByBroadcast(resultToEmart);
                sendActionToEmartByBroadcast(Constants.AP_ACTION_END);
            }

        } catch (Exception e) {
            Utils.LOGE(TAG, "clear reader: ", e);
        }
    }

    @Override
    public void cancelTimerCountDown() {
        if (timerPercent != null) {
            timerPercent.cancel();
            timerPercent = null;
        }
        if (scanTask != null) {
            scanTask.cancel();
            scanTask = null;
        }
    }

    @Override
    public void setPresenter(MposPaymentContract.Presenter presenter) {
        this.presenter = presenter;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Utils.LOGD(TAG, "onActivityResult() called with: requestCode = [" + requestCode + "], resultCode = [" + resultCode + "], data = [" + data + "]");

        Utils.LOGD(TAG, "onActivityResult: isDestroyed=" + isDestroyed());
        if (!isDestroyed()) {
            presenter.handleActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Utils.LOGD(TAG, "onRequestPermissionsResult called with: requestCode = [" + requestCode
                + "], grantResults.length = [" + grantResults.length + "], grantResults = [" + (grantResults.length>0?grantResults[0]:"empty") + "]");

        isSaveInstanceState = false;
        if (requestCode == MposPaymentPresenter.RQ_CODE_PERMISSION_BLE) {
            if (UtilsSystem.checkHaveBluetoothPermission(this)) {
                presenter.processConnectDevice();
            } else {
                MyDialogShow.showDialogWarning(this, getString(R.string.msg_request_permission_bluetooth), getString(R.string.ALERT_BTN_RETRY),
                        view -> requestEnableBlePermission(), view -> callbackCancel(), true
                );
            }
        }
        else if (requestCode == MposPaymentPresenter.RQ_CODE_PERMISSION_LOCATION) {
            if (grantResults.length > 0) {
                if (grantResults[0] != PackageManager.PERMISSION_GRANTED) { //grantResults.length == 1 &&
                    MyDialogShow.showDialogWarning(this, getString(R.string.need_allow_mpos_access_location), getString(R.string.ALERT_BTN_RETRY),
                            view -> requestLocationPermission(), view -> callbackCancel(), true
                    );
                } else {
                    presenter.checkBeforePay();
                }
            }
        }
    }

    @Override
    public void onBackPressed() {
        appendLog("** press key back **");
        exitPayment();
    }

    private void exitPayment() {
        if (!checkCanExit()) {
            showDialogCanNotExitWhilePay();
            return;
        }
        MyDialogShow.showDialogInfo(this, getString(R.string.ALERT_PAYMENT_CANCEL_TRANSACTION_MSG), false,
                getString(R.string.ALERT_BTN_OK), getString(R.string.ALERT_BTN_NO),
                view -> {
                    if (checkCanExit()) {
                        callbackCancel();
                    }
                    else {
                        showDialogCanNotExitWhilePay();
                    }
                },
                view -> {});
    }

    private boolean checkCanExit() {
        getPercentByStageProcess();
        appendLog("checkCanExit: currStage=" + currStageProcess);
        return currStageProcess <= LibReaderController.STAGE_PROCESS_START;
    }


    public void showDialogWarningUpgradeEmvConfig() {
        getNumSaleRemain();
        handleActionTracking(DataTracking.ActionTracker.update_config, "Warning update, numRemain="+numSaleRemain);

        Dialog dialog = new Dialog(this, R.style.SpecialDialog);

        DialogMposNtfBinding binding = DialogMposNtfBinding.inflate(getLayoutInflater());

        binding.title.setText(getString(R.string.mp_notice));

        String msg = getString(R.string.warning_have_upgrade_evm_config);
        binding.tvContent.setText(msg);

        binding.tvContent.setVisibility(View.VISIBLE);

        binding.ok.setText(getString(R.string.ALERT_BTN_UPDATE_NOW));
        binding.ok.setOnClickListener(v -> {
            presenter.downloadEmvConfig();
            ScreenUtils.dismissDialog(dialog);
            handleActionTracking(DataTracking.ActionTracker.update_config, "Update");
        });
        if (!DevicesUtil.isSP02() && numSaleRemain > 0) {
            binding.btnClose.setText(String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSaleRemain));
            binding.btnClose.setVisibility(View.VISIBLE);
            binding.vSpace.setVisibility(View.VISIBLE);
            binding.btnClose.setOnClickListener(v ->{
                appendLog("cancel update config");
                skipUpdate();
                handleActionTracking(DataTracking.ActionTracker.update_config, "Skip");
                ScreenUtils.dismissDialog(dialog);
            });
        }
        else {
            binding.btnClose.setVisibility(View.GONE);
        }

        dialog.setCancelable(false);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        if (dialog.getWindow() != null) {
            dialog.getWindow().getAttributes().windowAnimations = R.style.PauseDialogAnimation;
        }

        dialog.setCanceledOnTouchOutside(false);
        dialog.setContentView(binding.getRoot());
        dialog.show();
    }

    @Override
    public void showDialogWarningUpgradeFw() {
        getNumSaleRemain();
        handleActionTracking(DataTracking.ActionTracker.update_firmware, "Warning upgrade, numRemain="+numSaleRemain);
        MyDialogShow.showDialogWarning(this,
                DevicesUtil.isP20L() ? getString(R.string.p20l_warning_upgrade_fw) : getString(R.string.warning_upgrade_fw),
                getString(R.string.BTN_UPGRADE),
                getNameButtonSkipUpgradeFw(),
                view -> {
                    handleActionTracking(DataTracking.ActionTracker.update_firmware, "Upgrade");
                    presenter.upgradeFirmware();
                },
                numSaleRemain > 0 ? view -> {
                    appendLog("cancel upgrade fw");
                    handleActionTracking(DataTracking.ActionTracker.update_firmware, "Skip");
                    skipUpdate();
                } : null,
                false);
    }

    int numSaleRemain = 0;

    private String getNameButtonSkipUpgradeFw() {
        if (Constants.isTypeIntegrationMpos()) {
            return getString(R.string.ALERT_BTN_LATER);
        }else{
            return numSaleRemain > 0 ? String.format(Locale.getDefault(), "%s(%d)", getString(R.string.ALERT_BTN_LATER), numSaleRemain) : "";
        }
    }

    private void getNumSaleRemain() {
        if (Constants.isTypeIntegrationMpos()) {
            numSaleRemain = 7;
        }
        else {
            numSaleRemain = PrefLibTV.getInstance(this).get(PrefLibTV.numSaleRemain, Integer.class,
                    dataPrePay.getReaderType().getReaderType() != ConstantsPay.DEVICE_P20L ? Constants.NUM_SALE_REMAIN_DEFAULT : Constants.NUM_SALE_REMAIN_DEFAULT_UPGRADE_FW_SP01);
        }
        appendLog("numSaleRemain: " + numSaleRemain);
    }

    private void skipUpdate() {
        presenter.saveNumSaleRemain(numSaleRemain - 1);
        if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {
            callbackCancel();
        }
        else {
            presenter.checkBeforePay();
        }
    }

    private void showDialogCanNotExitWhilePay() {
        MyDialogShow.showDialogError(getString(R.string.warning_canot_exit_while_pay), this);
    }

    @Override
    public void showDialogAction(String msg, View.OnClickListener onclick) {
        MyDialogShow.showDialogWarning(this, msg, getString(R.string.LOGIN_BTN_CONTINUE), onclick, false);
    }

    @Override
    public void showDialogWaitSignature(final DataReversalLogin dataReversal, ClickListeners onclick) {
        DialogDetailWaitSignature mdialog = new DialogDetailWaitSignature();
        mdialog.setCancelable(false);
        mdialog.initVariable(dataReversal.transactionDate, dataReversal.pan,
                dataReversal.amount, dataReversal.cardholderName, dataReversal.itemDesc);

        mdialog.setClickListener(new DialogDetailWaitSignature.OnMyClickListener() {
            @Override
            public void clickOk(DialogFragment d, String email) {
                appendLog("- select continue");
                if (onclick != null) {
                    onclick.onClick(email);
                }
                d.dismiss();
            }

            @Override
            public void clickCancel(DialogFragment d) {
                appendLog("- select cancel");
            }
        });
        mdialog.show(getSupportFragmentManager(), DialogDetailWaitSignature.class.getName());
    }

    @Override
    public void showDialogFailPay(MposPaymentModel.DataFailPay dataFailPay) {
        handleActionTracking(DataTracking.ActionTracker.error, dataFailPay.dataError.getMsg());
        Utils.LOGD(TAG, ">>> ERROR USE CARD ON PR \n>>> errorCode: " + dataFailPay.dataError.getErrorCode()
                + " \n>>> errorMsg: " + dataFailPay.dataError.getMsg());
        if (sdkCustom != null && dataFailPay.dataError.getErrorCode() == ConstantsPay.ERROR_CODE_TIMEOUT_SWIPE_CARD
                && !sdkCustom.isShowDialogTimeoutSwipeCard()) {
            callbackFail(dataFailPay.dataPay, dataFailPay.dataError);
            return;
        }
        if (dataFailPay.dataError.isCancelRetrySignature()) {
            callbackUnsign(dataPrePay, dataFailPay.dataPay);
            return;
        }

        Utils.LOGD(TAG, "showDialogFailPay: isSaveInstanceState=" + isSaveInstanceState + " canShowDialog=" + ScreenUtils.canShowDialog(this));
        if (isSaveInstanceState || !ScreenUtils.canShowDialog(this)) {
            Utils.LOGD(TAG, "showDialogFailPay: cache and wait resume to show-----------");
            waitShowDialogFail = true;
            dataFailPayCache = dataFailPay;
        }
        else {
            Utils.LOGD(TAG, "showDialogFailPay: ------->>>");
            final MposDialog dialogErrorSwipeCard = MposDialog.newInstance(
                    MposPaymentActivity.this,
                    dataFailPay.dataError.getErrorCode(),
                    dataFailPay.dataError.getMsg(),
                    MposPaymentActivity.class.getName());

            dialogErrorSwipeCard.setOnClickListenerDialogClose(v -> {
                ScreenUtils.dismissDialog(dialogErrorSwipeCard);
                if (dataFailPay.typeFail == LibReaderController.TYPE_ERROR_CONFIRM_PAYMENT_FAILURE
                        || dataFailPay.typeFail == LibReaderController.TYPE_ERROR_CONFIRM_PAYMENT_FAIL_SERVER
                ) {
                    callbackUnsign(dataPrePay, dataFailPay.dataPay);
                }
                else {
                    callbackFail(dataFailPay.dataPay, dataFailPay.dataError);
                }
            });

            dialogErrorSwipeCard.show();
        }
    }

    @Override
    public void showViewMutilTrxPending(List<WfDetailRes> arr){

    }

    @Override
    public void onSuccessPay(DataPrePay dataPrePay, DataPay dataPay) {
        handleActionTracking(DataTracking.ActionTracker.success, dataPay.getAmount());
        putUpdateConfigToCallback(dataPrePay);
        callbackWithData(mposIntegrationHelper.buildDataSuccess(dataPrePay, dataPay));
    }

    @Override
    public void onSuccessVoid(DataPrePay dataPrePay, DataPay dataPay) {

        callbackWithData(mposIntegrationHelper.buildDataVoided(dataPrePay, dataPay));
    }

    @Override
    public void onSuccessPayOffline(DataPrePay dataPrePay, DataOfflinePay dataOfflinePay) {

    }

    @Override
    public <T> void onSuccessByAction(T t, MposTransactions.ActionTrans typeAction) {
        switch (typeAction) {
            case TRANS_STATUS:
                TransItem transItem = (TransItem) t;
                callbackTransactionStatus(transItem);
                break;
            case LIST_TRANS_PENDING_SIG:
                TransItem transItemPending = (TransItem) t;
                callbackListPendingTrans(transItemPending);
                break;
        }
    }

    @Override
    public void gotoScreenUpgradeFwSp02() {
        Intent intent = new Intent(this, P20LUpgradeFwActivity.class);
        startActivityForResult(intent, MposPaymentPresenter.RQ_CODE_UPGRADE_FW);
    }

    private void appendLog(String msg) {
        Utils.LOGD(TAG, "appendLog: " + msg);
        presenter.appendLog(ItfAppendLog.TypeLog.action, msg);
    }

    /**  __________________________ CALLBACK TO PARTNER __________________________
     * ----------*----------*----------*----------*----------*/
    private void putUpdateConfigToCallback(DataPrePay dataPrePay) {
        boolean needUpdate = PrefLibTV.getInstance(this).get(PrefLibTV.upgradeEMVConfig, Boolean.class, false)
                || PrefLibTV.getInstance(this).get(PrefLibTV.upgradeFw, Boolean.class, false);
        dataPrePay.setNeedUpdateConfig(needUpdate);
        if (needUpdate) {
            int numSaleRemain = PrefLibTV.getInstance(this).get(PrefLibTV.numSaleRemain, Integer.class, Constants.NUM_SALE_REMAIN_DEFAULT);
            dataPrePay.setNumberSaleRemain(numSaleRemain);
        }
        dataPrePay.setReaderSerial(payModel.getSerialnumber());
    }

    @Override
    public void callbackToPartner(String status, DataError dataError, DataPrePay dataPrePay, DataPay dataPay) {
        switch (status) {
            case TRANS_STATUS_ERROR:
                callbackFail(dataPay, dataError);
                break;
            case TRANS_STATUS_CANCEL:
                callbackCancel();
                break;
            case STATUS_UPDATED:
                callbackUpdated();
                break;
        }
    }

    private void callbackUnsign(DataPrePay dataPrePay, DataPay dataPay) {

        callbackWithData(mposIntegrationHelper.buildDataUnsign(dataPrePay, dataPay));
    }

    private void callbackFail(DataPay dataPay, DataError dataError) {
        putUpdateConfigToCallback(dataPrePay);
        callbackWithData(mposIntegrationHelper.buildDataFail(dataPrePay, dataPay, dataError));
    }

    private void callbackCancel() {
        putUpdateConfigToCallback(dataPrePay);
        callbackWithData(mposIntegrationHelper.buildDataCancel(dataPrePay, null));
    }

    private void callbackNotFoundUnsign() {

        callbackWithData(mposIntegrationHelper.buildDataNotFoundUnsign(dataPrePay, null));
    }


    private void callbackTransactionStatus(TransItem transItem) {
        callbackWithData(mposIntegrationHelper.buildDataTransItem(transItem));
    }

    private void callbackListPendingTrans(TransItem transItem) {
        callbackWithData(mposIntegrationHelper.buildDataListPendingTrans(transItem));
    }

    private void callbackUpdated() {
        callbackWithData(mposIntegrationHelper.buildDataReaderUpdated(dataPrePay));
    }

    String resultToEmart;
    private void callbackWithData(String data) {
        resultToEmart = data;

        SaveLogController.getInstance(this).appendLogActionCallback(data);
        SaveLogController.getInstance(this).pushLog();
        Intent intent = new Intent();

        Bundle bundle = new Bundle();
        bundle.putString(Intents.EXTRA_DATA_CALLBACK, data);
        intent.putExtras(bundle);

        setResult(RESULT_OK, intent);
        finish();
    }

    ReceiverTCPCancelOrder receiverTCPCancelOrder;
    ReceiverHandleAction receiverHandleAction;
    private void registerReceiverCancelOrder() {
        Utils.LOGD(TAG, "registerReceiverCancelOrder");

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intents.nameFilterActionCancelOrder);

        receiverTCPCancelOrder = new ReceiverTCPCancelOrder();
        try {
            //Register or UnRegister your broadcast receiver here
//            registerReceiver(receiverTCPCancelOrder, filter);
            ContextCompat.registerReceiver(MposPaymentActivity.this, receiverTCPCancelOrder, filter, ContextCompat.RECEIVER_NOT_EXPORTED);
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException " + e.getMessage());
            e.printStackTrace();
            receiverTCPCancelOrder = null;
        }
    }
    private void registerReceiverHandleAction() {
        Utils.LOGD(TAG, "registerReceiverCancelOrder");

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intents.nameFilterActionToPayment);

        receiverHandleAction = new ReceiverHandleAction();
        try {
            //Register or UnRegister your broadcast receiver here
//            registerReceiver(receiverHandleAction, filter);
            ContextCompat.registerReceiver(MposPaymentActivity.this, receiverHandleAction, filter, ContextCompat.RECEIVER_NOT_EXPORTED);
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException " + e.getMessage());
            e.printStackTrace();
            receiverHandleAction = null;
        }
    }

    private void unregisterReceiver() {
        if (receiverTCPCancelOrder != null) {
            unregisterReceiver(receiverTCPCancelOrder);
        }
        if (receiverHandleAction != null) {
            unregisterReceiver(receiverHandleAction);
        }
    }

    public class ReceiverTCPCancelOrder extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Utils.LOGD(TAG, "ReceiverTCPCancelOrder onReceive" + intent.getAction());
            appendLog("ReceiverTCPCancelOrder onReceive");
            if (intent.getBooleanExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, false)) {
                String dataCancelOrder = mposIntegrationHelper.buildDataCancel(dataPrePay, null);
                if (!TextUtils.isEmpty(dataCancelOrder)) {
                    if (checkCanExit()) {
                        //callback cancelOrder
                        if (PrefLibTV.getInstance(context).getPermitSocket()) {
                            sendResultCancelOrderToEmartByBroadcast(dataCancelOrder, true);
                        }
                        //callback cancelAddOrder
                        callbackCancel();
                    } else {
                        if (PrefLibTV.getInstance(context).getPermitSocket()) {
                            sendResultCancelOrderToEmartByBroadcast(dataCancelOrder, false);
                        }
                    }
                }
            }
        }
    }

    public class ReceiverHandleAction extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            Utils.LOGD(TAG, "ReceiverHandleAction onReceive" + intent.getAction());
            String action = intent.getStringExtra(Intents.EXTRA_DATA_BC_HANDLE_ACTION);
            appendLog("ReceiverHandleAction onReceive action: " + action);
            presenter.handleActionFromBroadcast(action, intent);

        }
    }
}
