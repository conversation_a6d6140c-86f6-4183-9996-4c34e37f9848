package com.mpos.sdk.screen;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;

import com.dspread.xpos.QPOSService;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.dspread.DeviceListActivity;
import com.mpos.sdk.MposPresenter;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.UiCustomUtil;
import com.mpos.sdk.core.control.DownloadConfigReader;
import com.mpos.sdk.core.control.GetData;
import com.mpos.sdk.core.control.JsonParser;
import com.mpos.sdk.core.control.LibDspreadReader;
import com.mpos.sdk.core.control.LibInjectKey;
import com.mpos.sdk.core.control.LibKozenP5;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.LibPax;
import com.mpos.sdk.core.control.LibReaderConfig;
import com.mpos.sdk.core.control.LibReaderController;
import com.mpos.sdk.core.control.LibReportError;
import com.mpos.sdk.core.control.LibSignature;
import com.mpos.sdk.core.control.LocationManagerMp;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.MposTransactions;
import com.mpos.sdk.core.control.MposTransactionsMacq;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.control.SoundEffects;
import com.mpos.sdk.core.model.AmountLimit;
import com.mpos.sdk.core.model.BankInstallment;
import com.mpos.sdk.core.model.BaseObjJson;
import com.mpos.sdk.core.model.BluetoothReaderPair;
import com.mpos.sdk.core.model.CommonConfig;
import com.mpos.sdk.core.model.DataCache;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.EmvConfigSp01;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.TransItem;
import com.mpos.sdk.core.modelma.McpInfo;
import com.mpos.sdk.core.modelma.PromotionInfo;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.*;

import com.mpos.sdk.view.BottomSheetMultiCurrentcy;
import com.mpos.sdk.view.BottomSheetPromotion;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Locale;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;

import static android.app.Activity.RESULT_OK;
import static com.mpos.sdk.core.control.LibReaderConfig.UI_STAGE_GET_MERCHANT_INFO;
import static com.mpos.sdk.core.control.LibReaderController.UI_STAGE_CANCEL_SCAN_DEVICE;
import static com.mpos.sdk.core.control.MposIntegrationHelper.STATUS_UPDATED;
import static com.mpos.sdk.core.control.MposIntegrationHelper.TRANS_STATUS_CANCEL;
import static com.mpos.sdk.core.control.MposIntegrationHelper.TRANS_STATUS_ERROR;
import static com.mpos.sdk.util.Constants.TIME_FETCH_NEW_CM_CONFIG;
import static com.mpos.sdk.util.Constants.TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_DEFAULT;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_DOWNLOAD_FW;

public class MposPaymentPresenter extends MposPresenter implements MposPaymentContract.Presenter, LibReaderController.ItfResultPay,
        LibLoginHandler.ItfHandlerResultLogin,LibLoginHandler.ItfHandlerTransWaitSignature, LibDspreadReader.ResultConnectDspread, ItfAppendLog
{

    private static final String TAG = "MposPaymentPresenter";

    public static final int RQ_CODE_PERMISSION_LOCATION = 88;
    public static final int RQ_CODE_ENABLE_GPS          = 77;
    public static final int RQ_CODE_PERMISSION_BLE      = 66;
    public static final int RQ_CODE_UPGRADE_FW          = 99;

    private final MposPaymentContract.View viewer;
    private LibReaderController readerController;
    private LibDspreadReader dspreadControl;
    private LibP20L libP20L;
    private LibKozenP5 libKozenP5;
    private LibPax libPax;

    private LibLoginHandler libLoginHandler;
    private LibInjectKey libInjectKey;
    private final LocationManagerMp locationManagerMp;
    private final MposTransactions mposTrans;

    private boolean isSuccessLogin;
    private boolean isRunMacq = false;
    private boolean isCheckedLimitAmount = false;
    private boolean isRunningUpgradeFwPr02 = false;
    private DataPay dataPay;
    private DataPay dataPayWaitSignature;
    private DataPrePay dataPrePay;
    private MposPaymentModel model;
    private final SaveLogController sv;

    private String currBankName;
    private int deviceType;

    @SuppressLint("MissingPermission")
    MposPaymentPresenter(@NonNull MposPaymentActivity context, @NonNull MposPaymentContract.View viewer, @NonNull MposPaymentModel paymentModel) {
        super(context);
        Utils.LOGD(TAG, "MposPaymentPresenter: <---init presenter-->");
        this.viewer = viewer;
        this.model = paymentModel;
        sv = SaveLogController.getInstance(context);
        mposTrans = new MposTransactions(context);
        locationManagerMp = new LocationManagerMp(context);
    }


    @Override
    public void start() {
        startTransaction();
        if (!PrefLibTV.getInstance(context).getPermitSocket()) {
            processCacheError();
        }
    }

    public void setDataPrePay(@NonNull DataPrePay dataPrePay) {
        this.dataPrePay = dataPrePay;
    }

    void setDataPayWaitSignature(DataPay dataPayWaitSignature) {
        this.dataPayWaitSignature = dataPayWaitSignature;
    }

    private void startTransaction() {
        setupReader();

        String udidReSignature = dataPayWaitSignature != null ? dataPayWaitSignature.getUdid() : "";
        Utils.LOGD(TAG, "initMoreInfoTrans: udidReSignature=" + udidReSignature);
        saveLAct("*** SDK start handler *** "
                + " act=" + dataPrePay.getActionType()
                + " ORDERID=" + dataPrePay.getOrderId()
                + " UDID=" + dataPrePay.getUdid()
                + (TextUtils.isEmpty(udidReSignature) ? "" : " -resign UDID=" + udidReSignature)
                + " TRANDID=" + dataPrePay.getTxId()
                + " Amount=" + dataPrePay.getAmount()
        );

        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && !TextUtils.isEmpty(udidReSignature)) {
            initLibReaderForSignature(dataPayWaitSignature);
            return;
        }

        processConnectDevice();
    }

    private void processCacheError() {
        LibReportError libReportError = new LibReportError(context);
        libReportError.processSendCacheData();
    }

    private void setupReader() {
        Utils.LOGD(TAG, "setupReader: dataPrePay.getReaderType()=" + dataPrePay.getReaderType());

        deviceType = dataPrePay.getReaderType().getReaderType();
//        if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.AUDIO) {
//            readerController = setupAudioReader();
//        }
//        else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.PR01) {
//            readerController = setupPr01();
//        }
//        else
        if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP01) {
            readerController = setupSP01();
        }
        else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP02) {
            readerController = setupSP02();
        }
        else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.PAX) {
            readerController = setupPax();
        }
        else {
            if (deviceType == ConstantsPay.DEVICE_NONE) {
                deviceType = ConstantsPay.DEVICE_DSPREAD;
            }
            readerController = setupPr02();
        }

        if (readerController != null) {
            setupGeneralReader(readerController);
        }
    }

    private LibPax setupPax() {
//        libPax = new LibPax(context, dataPrePay, viewer.getItfUpdateViewPax(), this);
        libPax = new LibPax(context, dataPrePay, viewer.getItfUpdateViewPr02(), this);
        return libPax;
    }

    private void setupGeneralReader(LibReaderController readerController) {
        Utils.LOGD(TAG, "setupGeneralReader: ---->");
        readerController.setCallBackSaveLog(this);

        readerController.setUseNewSignature(true);
        readerController.setShowErrorCodeInMsg(false);
        readerController.setSdkCustom(UiCustomUtil.getInstance().getSdkCustom());

        readerController.setCheckMaskPan(true);
        readerController.setItfCheckMaskPan(itfCheckMaskPan);
        readerController.setItfHandleActionTracking(this::handleActionTracking);

        if (PrefLibTV.getInstance(context).get(PrefLibTV.onOffPromotion, Boolean.class, false)) {
            readerController.setItfHandlePromotion(this::handlePromotion);
        }

//        if (PrefLibTV.getInstance(context).get(PrefLibTV.onOffMcp, Boolean.class, false)) {
//            readerController.setItfHandleMcp(this::handleMcp);
//        }

        if (PrefLibTV.getInstance(context).getPermitSocket()) {
            Utils.LOGD(TAG, "setup: --> is emart");
            readerController.setItfHandleBeforeSignature(dataPay -> {
                Utils.LOGD(TAG, "onHandleBeforeSignature: ===>");
                appendLogAct("onHandleResultBeforeSignature: ===>");
                viewer.sendResultSuccessBeforeSignatureToEmartByBroadcast(dataPay);
            });
        }
    }

//    private void handleMcp(ArrayList<McpInfo> mcpInfos, long l) {
//        try {
//            long newAmount = Long.parseLong(mcpInfos.get(0).getAmount());
//            dataPrePay.setAmount(newAmount);
//        } catch (Exception e) {
//            Utils.LOGE(TAG, "Failed to parse mcp", e);
//        }
//    }

    private void handlePromotion(long newAmount) {
        try {
//            long newAmount = Long.parseLong(promotionInfo.getAmountPayment());
            dataPrePay.setAmount(newAmount);
//            viewer.resetViewByPromotionInfo(arrSelectedPromotions);
            viewer.resetAmountView();
        } catch (Exception e) {
            Utils.LOGE(TAG, "Failed to parse promotion", e);
        }
    }

    LibReaderController.ItfCheckMaskPan itfCheckMaskPan = maskPan -> {
        String blockBinRange = PrefLibTV.getInstance(context).get(PrefLibTV.BLOCK_PAN_RANGE, String.class);
        saveLAct("checkMaskPan:" + maskPan + " BinRange-Block:" + blockBinRange + " trxType=" + dataPrePay.getTrxType());
        if (ConstantsPay.TRX_TYPE_SERVICE.equals(dataPrePay.getTrxType())) {
            if (!checkBinIsBinLocal(maskPan)) {
                saveLAct("SERVICE BIN blocked");
                showError(new DataError(getString(R.string.error_service_cannot_pay_global_card)));
                return false;
            }
            saveLAct("SERVICE BIN pass");
        }
        //blockPinRange = "500000-599999|200000-299999|400000-499999"
        if (!TextUtils.isEmpty(blockBinRange) && !TextUtils.isEmpty(maskPan)) {
            CardUtils cardUtils = new CardUtils();
            boolean result = cardUtils.checkBinInRange(maskPan, blockBinRange);
            if (result) {
                saveLAct("BIN blocked");
                showError(new DataError(getString(R.string.error_merchant_not_accept_pan)));
                return false;
            }
            saveLAct("BIN pass");
        }
        if (model!=null && model.getBundleExtra() != null) {
            // check card can installment
            BankInstallment bankInstallment = (BankInstallment) model.getBundleExtra().getSerializable(Intents.EXTRA_DATA_INSTALLMENT);
            if (bankInstallment != null && !checkBinCanInstallment(maskPan, bankInstallment)) {
                return false;
            }
        }
        //"blacklistBinRangeByIssuer": [
        //        "970500|970622",
        //        "970440|970477"
        //    ],
        if (!isRunMacq && libLoginHandler != null) {
            CommonConfig cmConfig = libLoginHandler.getCmConfigFromCache();
            if (cmConfig != null) {
                CardUtils cardUtils = new CardUtils();
                boolean result = cardUtils.checkBinInRange(maskPan, cmConfig.getBlacklistBinRangeByIssuer());
                if (result) {
                    saveLAct("BIN in blacklist");
                    showError(new DataError(getString(R.string.error_bin_in_blacklist)));
                    return false;
                }
            }
        }
        // new installment: check same card with check-card-installment
        if (isRunMacq) {
            String maskPanInstallmentNeedCheck = PrefLibTV.getInstance(context).get(PrefLibTV.maskPanInstallmentNeedCheck, String.class, "");
            if (!TextUtils.isEmpty(maskPanInstallmentNeedCheck)) {
                appendLogAct("maskPan installment=" + maskPanInstallmentNeedCheck);
                CardUtils cardUtils = new CardUtils();
                if (cardUtils.compareMaskPan(maskPanInstallmentNeedCheck, maskPan)) {
                    return true;
                }
                else {
                    showError(new DataError(getString(R.string.error_bin_not_same_bin_installment)));
                    return false;
                }
            }
        }
        return true;
    };

    void handleActionTracking(String action, String msg) {
        viewer.handleActionTracking(action, msg);
    }

    private boolean checkBinIsBinLocal(String maskPan) {
        String listBinLocal = PrefLibTV.getInstance(context).get(PrefLibTV.listBinLocal, String.class);
        CardUtils cardUtils = new CardUtils();
        return cardUtils.checkBinIsBinLocal(maskPan, listBinLocal);
    }

    private boolean checkBinCanInstallment(String maskPan, BankInstallment bankInstallment) {
        boolean result = true;

        CardUtils cardUtils = new CardUtils();
        boolean isSupportBin8 = cardUtils.isSupportBin8(maskPan);

        String binPaid;
        try {
            binPaid = maskPan.substring(0, isSupportBin8 ? 8 : 6);
        } catch (Exception e) {
            appendLogAct("error substring binPaid=" + maskPan);
            e.printStackTrace();
            return true;
        }
        appendLogAct("SDK checkCardInstallment: binPaid: " + binPaid);

        boolean isCardPaidInBankSelected = false;
        if (isSupportBin8) {
            for (String bin : bankInstallment.binList) {
                // length of binPaid always 8, length of bin is 6 or 8
                if (binPaid.startsWith(bin)) {
                    isCardPaidInBankSelected = true;
                    break;
                }
            }
        }
        else {
            for (String bin : bankInstallment.binList) {
                // length of binPaid always 6, length of bin is 6 or 8
                if (bin.startsWith(binPaid)) {
                    isCardPaidInBankSelected = true;
                    break;
                }
            }
        }

        if (!isCardPaidInBankSelected) {
            // check problem and get msg error
            InstallmentUtils installmentUtils = new InstallmentUtils();
            String configFromMpos = PrefLibTV.getInstance(context).get(PrefLibTV.installmentInfo,String.class,"");
            HashMap<String, BankInstallment> hmBank = installmentUtils.parseHashMapBinList(configFromMpos);

            String msgError;
            if (hmBank.containsKey(binPaid)) {
                BankInstallment bankInstallmentPaid = hmBank.get(binPaid);
                appendLogAct("check match bankName: select:" + bankInstallment.bankName + " real:" + (bankInstallmentPaid == null ? "null" : bankInstallmentPaid.bankName));
                result = false;
                if (bankInstallmentPaid != null && !bankInstallmentPaid.bankName.equals(bankInstallment.bankName)) {
                    appendLogAct(String.format("SDK---> thẻ (%s) ko thuộc ngân hàng đã chọn (%s)", bankInstallmentPaid.bankName, bankInstallment.bankName));
                    msgError = getString(R.string.mp_error_installment_invalid_bank, maskPan, bankInstallment.bankName, "("+bankInstallmentPaid.bankName+")");
                } else {
                    appendLogAct(String.format("SDK---> thẻ (%s) ko thuộc ngân hàng đã chọn (%s)", maskPan, bankInstallment.bankName));
                    msgError = getString(R.string.mp_error_installment_invalid_bank, maskPan, bankInstallment.bankName, "");
                }
            }
            else {
                appendLogAct("SDK---> thẻ ko được ngân hàng support");
                result = false;
                msgError = getString(R.string.mp_error_installment_not_support_card, maskPan);
            }
            showError(new DataError(msgError));
        }

        return result;
    }

    private LibDspreadReader setupPr02() {
        Utils.LOGD(TAG, "setupPr02: -----");
        if (dspreadControl == null) {
            dspreadControl = new LibDspreadReader(context, dataPrePay, viewer.getItfUpdateViewPr02(), this);
            dspreadControl.setTypeController(LibDspreadReader.TYPE_CONTROLLER_GET_SERIAL_NUMBER);
            dspreadControl.setCallBackConnectDevice(this);
        }
        dspreadControl.setCfrFilterNameReader(true);
        dspreadControl.setCfrShowImgInfo(true);

        dspreadControl.setCfrAutoScanReader(UiCustomUtil.getInstance().getSdkCustom().isAutoConnectPr02());

        dspreadControl.setCbRequestPermission(permissions -> viewer.requestEnableBlePermission());

        QPOSService.CardTradeMode cardTradeMode = model.getCardTradeModePr02();
        if (cardTradeMode != null) {
            dspreadControl.setCardTradeMode(cardTradeMode);
        }

        return dspreadControl;
    }

    private LibP20L setupSP01() {

        libP20L = new LibP20L(context, dataPrePay, viewer.getItfUpdateViewPr02(), this);
        return libP20L;
    }

    private LibKozenP5 setupSP02() {
        libKozenP5 = new LibKozenP5(context, dataPrePay, viewer.getItfUpdateViewPr02(), this);
        return libKozenP5;
    }

    public boolean checkRestrictNfc() {
        return readerController.checkRestrictionNfc();
    }

    @Override
    public void processConnectDevice() {
        if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP01) {
            if (DevicesUtil.isP20L()) {
                String serialNumber = libP20L.getSerialNumber();
                try {
                    appendLogAct("SP01 SDK-SN: " + serialNumber + " ->" + libP20L.getSmartPosApi().getDeviceSN());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(serialNumber)) {
                    showError(new DataError(ERROR_CODE_DEFAULT, getString(R.string.mp_error_cannot_get_SN_sp01)));
                }
                else {
                    onSuccessConnectDevice(new BluetoothReaderPair(serialNumber, ""));
                }
            }
        }
        else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.PAX) {
            // todo start Search Card
            if (DevicesUtil.isPax()) {
                String serialNumber = libPax.getSerialnumber();

                try {
                    appendLogAct("Pax get SN: " + serialNumber + " ->" + libPax.getSerialnumber());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(serialNumber)) {
                    showError(new DataError(ERROR_CODE_DEFAULT, getString(R.string.mp_error_cannot_get_SN_sp01)));
                }
                else {
                    onSuccessConnectDevice(new BluetoothReaderPair(serialNumber, ""));
                }
            }

        }
        else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP02) {
            if (DevicesUtil.isSP02()) {
                String serialNumber = libKozenP5.getSerialNumber();
                onSuccessConnectDevice(new BluetoothReaderPair(serialNumber, ""));
            }
        }
        else {
            dspreadControl.initPinpadStartTransaction();
        }
    }


    private void initUiReSignature(LibReaderController readerController, String udidReSignature) {
        try {
            if (!TextUtils.isEmpty(udidReSignature)) {
                udidReSignature = udidReSignature.substring(ConstantsPay.PREFIX_UDID_INSTALLMENT.length()).replace("_", " ");
            }
            Utils.LOGD(TAG, "initMoreInfoTrans: 11--udidReSignature=" + udidReSignature);

            int pos = udidReSignature.indexOf("/");
            if (pos > 0) {
                String descEn = udidReSignature.substring(0, pos);
                String descVi = udidReSignature.substring(pos + 1, udidReSignature.indexOf("-"));
                Utils.LOGD(TAG, "initMoreInfoTrans: 22--descEn=" + descEn + " descVi=" + descVi + " currLanguage=" + Locale.getDefault().getLanguage());
                readerController.setMoreInfoOfTrans(Locale.getDefault().getLanguage().equalsIgnoreCase("VI") ? descVi : descEn);
            }
        } catch (Exception e) {
            e.printStackTrace();
            readerController.setMoreInfoOfTrans("");
        }
    }

    private void initLibReaderForSignature(DataPay dataPay) {
        LibDspreadReader libReaderController = new LibDspreadReader(context);
        libReaderController.setCallback(this);
        initUiReSignature(libReaderController, dataPay.getUdid());

        setupGeneralReader(libReaderController);

        dataPay.setResign(true);

        libReaderController.startSignature(dataPay);
    }

    private void runSkipSignature(DataReversalLogin dataReversal) {
        saveLAct("runSkipSignature");
        final DataPay dataPayResign = new DataPay(dataReversal);
        LibSignature libSignature = new LibSignature(context, dataPayResign, new LibSignature.ItfResultSignature() {
            @Override
            public void appendLogSignature(String s) {
                saveLAct(s);
            }

            @Override
            public void onFailureSignature(int typeFail, DataError dataError, int typeVoidFail, boolean requestLogin) {
                saveLAct("-----Fail auto reSignature------" + (dataError != null ? dataError.getMsg() : ""));
                onFailPay(dataPayResign, dataError, typeFail, typeVoidFail, requestLogin);
            }

            @Override
            public void onSuccessSignature() {
//            public void onSuccessSignature(WfDetailRes detailRes) {
                saveLAct("-----Success auto reSignature------");
                dataPay = dataPayResign;
//                dataPay.setWfDetailRes(detailRes);
                finishPay();
            }
        });
        libSignature.confirmPayment(null, 1);
    }

    @Override
    public int getCurrStageProcess() {
        return readerController == null ? LibReaderConfig.STAGE_PROCESS_NONE : readerController.getCurrStageProcess();
    }

    @Override
    public boolean isRunningUpgradeFwPr02() {
        return isRunningUpgradeFwPr02;
    }

    @Override
    public void handleActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if (requestCode == RQ_CODE_ENABLE_GPS) {

            return;
        }
        else if (requestCode == RQ_CODE_UPGRADE_FW) {
            if (resultCode == RESULT_OK) {
                tickUpdateFwSuccess();
            } else {
                MyDialogShow.showDialogError(context, getString(R.string.error_upgrade_fw), getString(R.string.ok), null,
                        v -> callbackConfigOrCheckBeforePay(), null);
            }
            return;
        }

        if (dspreadControl != null) {
            switch (requestCode) {
                case LibReaderController.REQUEST_ENABLE_BT: {
                    if (resultCode == RESULT_OK) {
                        dspreadControl.checkAutoConnectReader();
                    }
                    else {
                        model.setCurrStageUi(UI_STAGE_CANCEL_SCAN_DEVICE);
                    }
                    break;
                }
                case LibReaderController.REQUEST_DEVICE_DSPREAD: {
                    if (resultCode == RESULT_OK && data != null && data.getExtras() != null) {
                        handSelectDevice(data);
                    }
                    else {
                        model.setCurrStageUi(UI_STAGE_CANCEL_SCAN_DEVICE);
                    }
                }
                default:
                    break;
            }
        }
    }

    private void handSelectDevice(@NonNull Intent data) {
        if (data.getExtras()!=null) {
            String address = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_ADDRESS);
            String serialnumber = data.getExtras().getString(DeviceListActivity.EXTRA_DEVICE_SERIALNUMBER);
            String snMustConnect = PrefLibTV.getInstance(context).getSerialNumberMustConnect();
            Utils.LOGD(TAG, "handSelectDevice: serialnumber=" + serialnumber + " snMustConnect=" + snMustConnect);
            appendLogAct("selectDevice: serialnumber=" + serialnumber + " snMustConnect=" + snMustConnect);
            if (!TextUtils.isEmpty(snMustConnect) && !snMustConnect.equals(serialnumber)) {
                showError(new DataError(ConstantsPay.ERROR_CODE_PR02_NOT_SAME_SN,
                        String.format("Vui lòng kết nối tới đúng thiết bị %s để tiếp tục sử dụng dịch vụ.", snMustConnect)));
                return;
            }
            dspreadControl.connectToDeviceByBluetoothAddress(new BluetoothReaderPair(serialnumber, address));

            model.setBluetoothAddress(address);
            model.setSerialnumber(serialnumber);
        }
    }

    @Override
    public void onDestroyActivity() {
        PrefLibTV.getInstance(context).put(PrefLibTV.maskPanInstallmentNeedCheck, "");
        killConnectReader();
        locationManagerMp.stopGetLocation();
    }

    public void killConnectReader() {
        sl();
        readerController.finishPayment();
    }

    private void hideLoading() {
        showLoading(false);
    }

    private void showLoading(String msg) {
        viewer.showLoading(msg, true);
    }
    @Override
    public void showLoading(boolean show) {
        Utils.LOGD(TAG, "showLoading: "+show);
        viewer.showLoading(null, show);
    }

    @Override
    public void onFailureLogin(@NonNull DataError dataError, int type) {
        Utils.LOGD(TAG, "onFailureLogin() called with: msgAlert = [" + dataError.getErrorCode()+" - "+dataError.getMsg() + "], type = [" + type + "]");
        saveLAct("login fail:" + dataError.getMsg());
        if (type == LibLoginHandler.TYPE_FAIL_FINISH) {
            viewer.callbackToPartner(TRANS_STATUS_CANCEL, dataError, dataPrePay, null);
        }
        else {
            viewer.showError(dataError);
        }
    }

    @Override
    public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {
        Utils.LOGD(TAG, "onHaveTranWaitSignature() called with: dataReversalLogin = [" + dataReversalLogin.amount + "]");
        saveLAct("login wait signture: udid-w=" + dataReversalLogin.paymentIdentify);
        if (MposIntegrationHelper.A_CONTINUE_TRANS.equals(dataPrePay.getActionType())
                || MposIntegrationHelper.A_CHECK_UNSIGNATURE_TRANS.equals(dataPrePay.getActionType())) {

            if (!TextUtils.isEmpty(dataPrePay.getUdid()) && dataPrePay.getUdid().equals(dataReversalLogin.paymentIdentify)) {
                processCheckSkipSignature(dataReversalLogin);
            }
            else {
                viewer.showError(getString(R.string.error_udid_not_duplicate));
            }
        }
        else {
            viewer.showDialogWaitSignature(dataReversalLogin,  (email) -> {
                if (!TextUtils.isEmpty((String) email) && !TextUtils.isEmpty(dataReversalLogin.email)) {
                    dataReversalLogin.email = (String) email;
                }
                processCheckSkipSignature(dataReversalLogin);
            });
        }
    }

    @Override
    public void onHaveTranWaitSignatureMultiAcquirer(@NonNull List<WfDetailRes> arr) {
        Utils.LOGD(TAG, "onHaveTranWaitSignatureMultiAcquirer: " + arr.size());
        if (MposIntegrationHelper.A_GET_LIST_WAIT_SIGN.equals(dataPrePay.getActionType())) {
            TransItem transItem = new TransItem(arr);
            viewer.onSuccessByAction(transItem, MposTransactionsMacq.ActionTrans.LIST_TRANS_PENDING_SIG);
        }
        else if (MposIntegrationHelper.A_CONTINUE_TRANS.equals(dataPrePay.getActionType())
                || MposIntegrationHelper.A_CHECK_UNSIGNATURE_TRANS.equals(dataPrePay.getActionType())) {
            if (TextUtils.isEmpty(dataPrePay.getUdid())) {
                viewer.showError(getString(R.string.error_udid_not_duplicate));
            } else {
                if (arr.size() == 1) {
                    if (dataPrePay.getUdid().equals(arr.get(0).getUdid())) {
                        processSignatureMA(arr.get(0));
                    }
                    else {
                        viewer.showError(getString(R.string.error_udid_not_duplicate));
                    }
                }
                else {
                    boolean haveInList = false;
                    for (WfDetailRes item : arr) {
                        if (item.getUdid().equals(dataPrePay.getUdid())) {
                            processSignatureMA(item);
                            haveInList = true;
                            break;
                        }
                    }
                    if (!haveInList) {
                        viewer.showError(getString(R.string.error_udid_not_duplicate));
                    }
                }
            }
        }
        else {
            viewer.showDialogWaitSignature(new DataReversalLogin(arr.get(0)), (email) -> {
                if (!TextUtils.isEmpty((String) email) && !TextUtils.isEmpty(arr.get(0).getCustomerEmail())) {
                    arr.get(0).setCustomerEmail((String) email);
                }
                processSignatureMA(arr.get(0));
            });
        }
    }

    private void processCheckSkipSignature(DataReversalLogin dataReversalLogin) {
        saveLAct("processCheckSkipSignature");
        PayUtils payUtils = new PayUtils();
        if (payUtils.checkSkipSignature(context, dataReversalLogin.amount)) {
            runSkipSignature(dataReversalLogin);
        }
        else {
            initLibReaderForSignature(new DataPay(dataReversalLogin));
        }
    }

    private void processSignatureMA(WfDetailRes detailRes) {
        initLibReaderForSignature(new DataPay(detailRes));
    }

    @Override
    public void onSuccessLogin() {
        // todo note: login success
        Utils.LOGD(TAG, "onSuccessLogin: -----action=" + dataPrePay.getActionType());
        isSuccessLogin = true;
        saveLAct("login success");
        if (DevicesUtil.isP20L()) {
            appendLogAct("devicePN=" + libP20L.getSmartPosApi().getDevicePN().getData());
        }
        currBankName = PrefLibTV.getInstance(context).getBankName();
        handlerBankSelected(currBankName);
        if (MposIntegrationHelper.A_CONTINUE_TRANS.equals(dataPrePay.getActionType())
                || MposIntegrationHelper.A_CHECK_UNSIGNATURE_TRANS.equals(dataPrePay.getActionType())) {
            showError(new DataError(ConstantsPay.ERROR_CODE_NOT_FOUND, getString(R.string.mp_error_not_unsign)));
        }
        else if (MposIntegrationHelper.A_VOID_TRANS.equals(dataPrePay.getActionType())) {
            if (PrefLibTV.getInstance(context).getPermitVoid()) {
                getSalesDetail(dataPrePay.getTxId());
            }
            else {
                callbackError(new DataError(ERROR_CODE_DEFAULT, getString(R.string.error_not_permission_void)));
            }
        }
        else if (MposIntegrationHelper.A_GET_STATUS.equals(dataPrePay.getActionType())) {
            getTransStatus();
        }
        else if (MposIntegrationHelper.A_GET_LIST_WAIT_SIGN.equals(dataPrePay.getActionType())) {
            if (isRunMacq) {
                TransItem transItem = new TransItem(new ArrayList<>());
                viewer.onSuccessByAction(transItem, MposTransactionsMacq.ActionTrans.LIST_TRANS_PENDING_SIG);
            }
            else {
                getListTransWaitSignature();
            }
        }
        else {
            // check firmware first
            if (PrefLibTV.getInstance(context).get(PrefLibTV.upgradeFw, Boolean.class, Boolean.FALSE)
                    && (deviceType == ConstantsPay.DEVICE_DSPREAD || deviceType == ConstantsPay.DEVICE_P20L)
            ) {
                if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
                    processUpgradeFwPr02();
                }
                else{
                    processUpgradeFwSp01();
                }
            }
            else if ( (!Utils.checkTypeBuildIsCertify() && PrefLibTV.getInstance(context).get(PrefLibTV.upgradeEMVConfig, Boolean.class, Boolean.FALSE))
                    || (!Utils.checkTypeBuildIsCertify() && !Utils.checkTypeBuildIsDebug()
                    && PrefLibTV.getInstance(context).get(PrefLibTV.retryUpdateEmvConfig, Boolean.class, Boolean.FALSE))
            ) {
                // All devices uses the config from macq -> macq has cached -> need cache and check in client
                if (isRunMacq) {
                    long lastTimeUpgradeEmvConfig = PrefLibTV.getInstance(context).get(PrefLibTV.lastTimeUpgradeEMVConfig, Long.class);
                    long aboutTime = System.currentTimeMillis() - lastTimeUpgradeEmvConfig;
                    // 8 minutes: 8 * 60 * 1000 = 480000
                    if (aboutTime > 480000) {
                        viewer.showDialogWarningUpgradeEmvConfig();
                    } else {
                        callbackConfigOrCheckBeforePay();
                    }
                } else {
                    viewer.showDialogWarningUpgradeEmvConfig();
                }
            }
            else {
                callbackConfigOrCheckBeforePay();
            }
        }
    }

    private void callbackConfigOrCheckBeforePay() {
        if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {
            callbackUpdateSuccessConfig();
        }
        else {
            checkBeforePay();
        }
    }

    private void processUpgradeFwSp01() {
        String versionNew = PrefLibTV.getInstance(context).get(PrefLibTV.versionFwUpgrade, String.class, "");

        if (libP20L.canUpgradeFw(versionNew)) {
            checkAndShowDialogWarningUpgradeFw();
        }
        else {
            callbackConfigOrCheckBeforePay();
        }
    }

    private void processUpgradeFwPr02() {
        String versionNew = PrefLibTV.getInstance(context).get(PrefLibTV.versionFwUpgrade, String.class, "");
        if (TextUtils.isEmpty(versionNew)) {
            checkAndShowDialogWarningUpgradeFw();
        }
        else {
            appendLogFw("get posInfo");
            showLoading(true);
            dspreadControl.setGetPosInfoForFw(true);
            dspreadControl.setCbGetPosInfo(new LibDspreadReader.ItfGetPosInfo() {
                @Override
                public void onGetPosInfo(Hashtable<String, String> posInfoData) {
                    showLoading(false);
                    String fwVersion = "";
                    if (posInfoData != null && posInfoData.contains("firmwareVersion")) {
                        fwVersion = posInfoData.get("firmwareVersion");
                    }
                    appendLogFw("currV:" + fwVersion + " newV:" + versionNew);
                    if (!versionNew.equals(fwVersion)) {
                        checkAndShowDialogWarningUpgradeFw();
                    }
                    else {
                        callbackConfigOrCheckBeforePay();
                    }
                }

                @Override
                public void onErrorGetPosInfo(QPOSService.Error typeError) {
                    showLoading(false);
                    appendLogFw("error posInfo -> update");
                    checkAndShowDialogWarningUpgradeFw();
                }
            });
            dspreadControl.getPos().getQposInfo();
        }
    }

    private void checkAndShowDialogWarningUpgradeFw() {
        if (needShowDialogConfirmUpdateFw()) {
            appendLogAct("show warning upgrade FW");
            viewer.showDialogWarningUpgradeFw();
        }
        else {
            upgradeFirmware();
        }
    }

    private boolean needShowDialogConfirmUpdateFw() {
        if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {
            return dataPrePay.isShowDialogConfirm();
        }
        return true;
    }

    @Override
    public void onSuccessConnectDevice(BluetoothReaderPair devicePair) {
        Utils.LOGD(TAG, "onSuccessConnectDevice: "+devicePair.name);
        saveLAct("connect device success: SERIAL=" + devicePair.name);
        model.setBluetoothAddress(devicePair.addr);
        model.setSerialnumber(devicePair.name);
        // update view -> connected
        viewer.updateViewByStatePR(LibReaderController.UI_STAGE_CONNECTED_DEVICE);
        if (isSuccessLogin) {
            checkBeforePay();
        }
        else {
            processLogin(devicePair.name);
        }
    }

    @Override
    public void processLogin(String serialNumber) {
        // todo note: login
        viewer.getItfUpdateViewPr02().showViewDspreadByStage(UI_STAGE_GET_MERCHANT_INFO);
        libLoginHandler = new LibLoginHandler(context, this, deviceType);//dataPrePay.getReaderType().getReaderType());

        String languageCode = PrefLibTV.getInstance(context).getCustomLanguage();
        if (TextUtils.isEmpty(languageCode)) {
            languageCode = Locale.getDefault().getLanguage();
        }
        appendLogAct("use language:" + languageCode + " bName=" + dataPrePay.getbName());
        if (!TextUtils.isEmpty(languageCode)) {
            if (languageCode.equals(LanguageCode.LANGUAGE_VI.getLanguageCode())) {
                libLoginHandler.setCustomLanguage(LanguageCode.LANGUAGE_VI);
            }
            else {
                libLoginHandler.setCustomLanguage(LanguageCode.LANGUAGE_EN);
            }
        }

        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())
                && !TextUtils.isEmpty(dataPrePay.getAppType())) {
            libLoginHandler.setAppType(dataPrePay.getAppType());
        }

        if (MposIntegrationHelper.A_VOID_TRANS.equals(dataPrePay.getActionType())) {
            libLoginHandler.setLevelLogin(2);
        }
        else {
            if (!MposIntegrationHelper.A_GET_STATUS.equals(dataPrePay.getActionType())
                    && !MposIntegrationHelper.A_GET_LIST_WAIT_SIGN.equals(dataPrePay.getActionType())
                    && !model.isSkipWaitSignature()
            ) {
                libLoginHandler.setHandlerTranWaitSignature(true);
            }
            libLoginHandler.setItfHandlerWaitSignatureMA(this);
        }

//        libLoginHandler.setCanUseStorageCacheFetch(true);
        libLoginHandler.setTypeUseCache(TYPE_CACHE_SESSION_IN_DAY_AND_ONFAIL_API_FOREVER);

        if ((DevicesUtil.isSP02P12() || DevicesUtil.isSP02N4()) && dataPrePay.isLoginByDevice()) {
            libLoginHandler.processLoginMacqForSale(dataPrePay.getMobileUser(), serialNumber);
        }
        else if (TextUtils.isEmpty(dataPrePay.getbName())) {
            libLoginHandler.fetchMerchantConfigMacq(dataPrePay.getMobileUser(), serialNumber, dataPrePay.getMobilePass());
        }
        else {
            libLoginHandler.selectFlowByBankName(dataPrePay.getMobileUser(), dataPrePay.getMobilePass(), dataPrePay.getbName(), serialNumber);
        }
    }

    private void handlerBankSelected(String bankName) {
        Utils.LOGD(TAG, "handlerBankSelected: bankName=" + bankName);
        if (ConstantsPay.MPOS_MULTI_ACQUIRER.equals(bankName)) {
            isRunMacq = true;
            readerController.setAutoCheckWaitSignWhenTimeOut(false);
        }
        else {
            readerController.setAutoCheckWaitSignWhenTimeOut(true);
        }
    }

    @Override
    public void onFailConnectDevice(BluetoothReaderPair devicePair) {
        saveLAct("connect device fail:" + devicePair.name);
        viewer.showToast(getString(R.string.msg_failed_to_connect_to_reader, devicePair.name));
        if (dspreadControl != null) {
            dspreadControl.startActivitySelectReader();
        }
    }

    @Override
    public void foundDevices(ArrayList<BluetoothDevice> arrDevices) {
        Utils.LOGD(TAG, "foundDevices: ======>?");
    }

    @Override
    public void handleActionFromBroadcast(String action, Intent intent) {
        if ("AppIsOpen".equals(action)) {
            readerController.setOpenAppAgain(true);
        }
    }

    @Override
    public void checkBeforePay() {
        boolean paynow = false;

        if (deviceType!=ConstantsPay.DEVICE_AUDIO && deviceType!=ConstantsPay.DEVICE_PINPAD
                && !runCheckAutoInject && checkNeedAutoInjectKey()) {
            return;
        }


        if (PrefLibTV.getInstance(context).getDisableCheckGps()) {
            appendLogAct("disable Check GPS");
            paynow = true;
        } else {
            if (!UtilsSystem.checkHaveLocationPermission(context)) {
                appendLogAct("request location permission");
                viewer.requestLocationPermission();
            }
            else if (!UtilsSystem.checkIsEnableGps(context)) {
                appendLogAct("show dialog enable GPS");
                viewer.showDialogEnableGps();
            }
            else {
//                appendLogAct("start get location");
                locationManagerMp.startGetLocation();
                paynow = true;
            }
        }

        if (paynow && !isCheckedLimitAmount && PrefLibTV.getInstance(context).get(PrefLibTV.mandatoryCheckLimit, String.class, "").equals(Constants.SVALUE_1)) {
            paynow = false;
            fetchLimitAmount();
        }

        if (paynow && !isRunMacq && checkNeedFetchBlacklistCard()) {
            paynow = false;
            fetchCommonConfig(true, (result, cmConfig) -> {
                if (!result) {
                    PrefLibTV.getInstance(context).put(PrefLibTV.commonConfig, "");
                }
                startPay();
            });
        }

        ArrayList<McpInfo> array = new ArrayList<>();
        array.add(new McpInfo("10000", "0", "VND", "Vietnam Dong"));
        array.add(new McpInfo("10000", "0", "USD", "United States Dollar"));
        if (paynow && PrefLibTV.getInstance(context).get(PrefLibTV.onOffMcp, Boolean.class, false) && array.size() > 0) {
            paynow = false;
            viewer.showDialogMultiCurrency(array, new BottomSheetMultiCurrentcy.ItfCallbackMcp() {
                @Override
                public void onCancelChooseMcp() {

                }

                @Override
                public void onPayWithMulticurrency(McpInfo arrMcp) {
                    dataPrePay.setAmount(Long.parseLong(arrMcp.getAmount()));
                    Utils.LOGD(TAG, "onPayWithMulticurrency: " + dataPrePay.getAmount());
                    readerController.updateAmountCurrentCy();
                    viewer.resetAmountView();
                    startPay();
                }
            });
        }

        if (paynow) {
            startPay();
        }
    }

    private boolean checkNeedFetchBlacklistCard() {
        Utils.LOGD(TAG, "checkNeedFetchBlacklistCard: --->");
        if (libLoginHandler != null) {
            CommonConfig cmConfig = libLoginHandler.getCmConfigFromCache();
            if (cmConfig != null && cmConfig.getBlacklistBinRangeByIssuer() != null
                    && cmConfig.getBlacklistBinRangeByIssuer().size() > 0) {
                return System.currentTimeMillis() - cmConfig.getTimeResponse() > TIME_FETCH_NEW_CM_CONFIG;
            }
        }
        return false;
    }

    private void startPay() {
        saveLAct("startPay: SERIAL=" + model.getSerialnumber() + " AMOUNT=" + dataPrePay.getAmount() + " destroy=" + model.isDestroyActivity());
        PrefLibTV.getInstance(context).setSerialNumber(model.getSerialnumber());
        if (UiCustomUtil.getInstance().getSdkCustom() != null && UiCustomUtil.getInstance().getSdkCustom().isCacheDeviceConnected()) {
            PrefLibTV.getInstance(context).setBluetoothAddress(model.getBluetoothAddress());
        }
        else {
            PrefLibTV.getInstance(context).setBluetoothAddress("");
        }
        Utils.LOGD(TAG, "startPay: reader="+model.getSerialnumber());
        if (model.isDestroyActivity()) {
            return;
        }

        readerController.startPayment();

    }

    @Override
    public void processSignatureWhenResumeScreen() {
        Utils.LOGD(TAG, "processSignatureWhenResumeScreen: currStage="
                + (readerController == null ? "can not get" : readerController.getCurrStageProcess()));
        if (readerController != null) {
            readerController.processSignatureWhenResumeScreen();
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        if (readerController != null) {
            readerController.onSaveInstanceState();
        }
    }

    public void resetSaveInstanceState() {
        if (readerController != null) {
            readerController.resetSaveInstanceState(false);
        }
    }

    /**------------------------ result payment ------------------------
     **/
    @Override
    public void onSuccessPay(DataPay dataPay) {
        viewer.cancelTimerCountDown();
        this.dataPay = dataPay;
        SoundEffects.getInstance().playSoundSuccess();
        checkAfterPaySuccess();
    }

    private void finishPay() {
        viewer.onSuccessPay(dataPrePay, dataPay);
    }

    @Override
    public void onFailPay(DataPay dataPay, DataError dataError, int typeFail, int typeVoidFail, boolean requestLogin) {
        sendErrorToMpos(dataPay, dataError);

        processCheckCommonConfig();

        if (isRunMacq && !Utils.checkTypeBuildIsCertify()) {
            // not show error code in dialog
            dataError.setErrorCode(0);
        }
        model.setDataFailPay(model.new DataFailPay(dataPay, dataError, typeFail, typeVoidFail));
        viewer.showDialogFailPay(model.getDataFailPay());
    }

    private void sendErrorToMpos(DataPay dataPay, DataError dataError) {
        LibReportError libReportError = new LibReportError(context);

        if (PrefLibTV.getInstance(context).getPermitSocket()) {
            String errorText = libReportError.buildDataError(dataPay, dataError, null);
            libReportError.processSaveAndSendCacheData(errorText);
        }
        else {
            libReportError.sendReportErrorToMpos(dataPay, dataError);
        }
    }

    private void sl() {
        if (sv != null) {
            sv.saveLog();
        }
    }

    private void processCheckCommonConfig() {
        boolean needFetch = true;
        if (libLoginHandler != null) {
            CommonConfig commonConfig = libLoginHandler.getCmConfigFromCache();
            if (commonConfig != null) {
               needFetch = (System.currentTimeMillis() - commonConfig.getTimeResponse() >= TIME_FETCH_NEW_CM_CONFIG);
            }
        }
        if (needFetch) {
            fetchCommonConfig(false,null);
        }
    }

    private void fetchCommonConfig(boolean showLoading, LibLoginHandler.ItfHandlerCommonConfig callback) {
        if (libLoginHandler != null) {
            libLoginHandler.fetchCommonConfig(isRunMacq, showLoading, callback);
        }
    }

    private void fetchLimitAmount() {
        if (!GetData.CheckInternet(context)) {
            appendLogApi(ConstantsPay.GET_LIMID_BY_MUID+" no network available");
            MyDialogShow.showDialogRetry(getString(R.string.check_internet), context, v -> fetchLimitAmount());
            return;
        }
        isCheckedLimitAmount = true;
        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.GET_LIMID_BY_MUID);

            jo.put("muid", PrefLibTV.getInstance(context).getUserId());
            String midBank = PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID_BANK, String.class, "");
            if (!TextUtils.isEmpty(midBank)) {
                jo.put("mid", midBank);
            }
            appendLogApi(ConstantsPay.GET_LIMID_BY_MUID + " mid=" + midBank);

            Utils.LOGD(TAG, "GET_LIMID_BY_MUID | REQ: " + jo);

            entity = new StringEntity(jo.toString());
        } catch (Exception e) {
            Utils.LOGE(TAG, "Exception", e);
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_API, entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onStart() {
                showLoading(true);
                super.onStart();
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                appendLogApi(ConstantsPay.GET_LIMID_BY_MUID + " onFailure: " + (arg3 != null ? arg3.toString() : ""));
                hideLoading();
                handleFailCheckLimit();
            }

            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                hideLoading();
                String msgError = null;
                try {
                    String data = new String(arg2);
                    Utils.LOGD(TAG, "GET_LIMID_BY_MUID onSuccess: " + data);
                    AmountLimit amountLimit = MyGson.parseJson(data, AmountLimit.class);

                    if (Constants.CODE_REQUEST_SUCCESS.equals(amountLimit.getError().code)) {
                        appendLogApi(ConstantsPay.GET_LIMID_BY_MUID + " success " + data);

                        checkAmountIsLimit(amountLimit);
                    } else {
                        msgError = getString(R.string.error_get_amount_limit);
                        appendLogApi(ConstantsPay.GET_LIMID_BY_MUID + " error:" + msgError);
                    }
                } catch (Exception e) {
                    appendLogApi(ConstantsPay.GET_LIMID_BY_MUID + " Exception:" + e.getMessage());
                    msgError = getString(R.string.error_try_again);
                    Utils.LOGE(TAG, "Exception", e);
                }
                if (!TextUtils.isEmpty(msgError)) {
                    handleFailCheckLimit();
                }
            }

            private void handleFailCheckLimit() {
                checkBeforePay();
            }
        });
    }

    private void checkAmountIsLimit(AmountLimit amountLimit) {

        String msgError = null;
        // limit with MID
        if (amountLimit.getMidMaxAmountPerTransation()>0 && dataPrePay.getAmount() > amountLimit.getMidMaxAmountPerTransation()) {
            msgError = getString(R.string.warning_limit_amount_trans,
                    Utils.zenMoney(amountLimit.getMidMaxAmountPerTransation()) + ConstantsPay.CURRENCY_SPACE_PRE);
        }
        else if (dataPrePay.getAmount() + amountLimit.getMidTotalAmountDay() > amountLimit.getMidMaxAmountPerDay()) {
            msgError = getString(R.string.warning_limit_amount_day);
        }
        else if (dataPrePay.getAmount() + amountLimit.getMidTotalAmountMonth() > amountLimit.getMidMaxAmountPerMonth()) {
            msgError = getString(R.string.warning_limit_amount_month);
        }
        // limit with MUID
        else if (amountLimit.getMaxAmountPerTransation()>0 && dataPrePay.getAmount() > amountLimit.getMaxAmountPerTransation()) {
            msgError = getString(R.string.warning_limit_amount_trans, Utils.zenMoney(amountLimit.getMaxAmountPerTransation()) + ConstantsPay.CURRENCY_SPACE_PRE);
        }
        else if (dataPrePay.getAmount() + amountLimit.getTotalAmountDay() > amountLimit.getMaxAmountPerDay()) {
            msgError = getString(R.string.warning_limit_amount_day);
        }
        else if (dataPrePay.getAmount() + amountLimit.getTotalAmountMonth() > amountLimit.getMaxAmountPerMonth()) {
            msgError = getString(R.string.warning_limit_amount_month);
        }
        Utils.LOGD(TAG, "checkAmountIsLimit: msgError: " + msgError);

        if (TextUtils.isEmpty(msgError)) {
            checkBeforePay();
        }
        else {
            showError(new DataError(ConstantsPay.ERROR_CODE_LIMIT_AMOUNT, msgError));
        }
    }

    @Override
    public void onFinishWithTypeError(int type) {

    }

    private void checkAfterPaySuccess() {

        processCheckCommonConfig();
        Utils.LOGD(TAG, "checkAfterPaySuccess: hasSendEmail:" + PrefLibTV.getInstance(context).get(PrefLibTV.sendTrxReceipt, Boolean.class, false));
        if (PrefLibTV.getInstance(context).get(PrefLibTV.sendTrxReceipt, Boolean.class, false)
//                && !TextUtils.isEmpty(dataPrePay.getEmail())
        ) {
//            sendEmailByType(dataPrePay.getEmail(), dataPay.getTxId());
            checkEmailPassengerForSend();
        }
        else {
            finishPay();
        }
    }

    private void checkEmailPassengerForSend() {
        if (!TextUtils.isEmpty(dataPrePay.getEmail())) {
            sendEmailByType(dataPrePay.getEmail(), dataPay.getTxId(), EMAIL_PASSENGER);
        }
        else {
            checkEmailMerchantForSend();
        }
    }

    private void checkEmailMerchantForSend() {
        String emailMerchant = PrefLibTV.getInstance(context).getEmailMerchant();
        if (TextUtils.isEmpty(emailMerchant)) {
            finishPay();
        } else {
            sendEmailByType(emailMerchant, dataPay.getTxId(), EMAIL_MERCHANT);
        }
    }

    private final byte EMAIL_PASSENGER = 0;
    private final byte EMAIL_MERCHANT = 1;
    private void sendEmailByType(String email, String txId, int typeEmail) {
        Utils.LOGD(TAG, "sendEmailByType() called with: email = [" + email + "], txId = [" + txId + "], typeEmail = [" + typeEmail + "]");
        if (!GetData.CheckInternet(context)) {
//            viewer.showToast(typeEmail == EMAIL_PASSENGER ? getString(R.string.error_send_email_invoice_passenger)
//                    : getString(R.string.error_send_email_invoice_merchant));
            finishPay();
            return;
        }
        appendLogAct("sendEmail to " + (typeEmail == EMAIL_PASSENGER ? "CardHolder" : "MC"));
        showLoading(true);
        initParamForMposTransaction();
        mposTrans.sendReceiptWithTransaction(txId, email, new MposTransactions.ItfHandlerActionTrans<Boolean>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
                appendLogAct("sendEmail fail");
//                showToastResultSendEmail(typeEmail, true);
                finishPay();
            }

            @Override
            public void onSuccessActionTrans(Boolean obj, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
//                finishPay();
                appendLogAct("sendEmail ok");
//                showToastResultSendEmail(typeEmail, false);

                if (typeEmail == EMAIL_PASSENGER) {
                    checkEmailMerchantForSend();
                }
                else {
                    finishPay();
                }
            }
        });
    }

//    private void showToastResultSendEmail(int typeEmail, boolean isError) {

        /*if (typeEmail == EMAIL_MERCHANT) {
            if (isError) {
                showToastNotify(getString(R.string.error_send_email_invoice_merchant));
            } else {
                showToastNotify(getString(R.string.success_send_email_invoice_merchant));
            }
        } else {
            if (isError) {
                showToastNotify(getString(R.string.error_send_email_invoice_passenger));
            } else {
                showToastNotify(getString(R.string.success_send_email_invoice_passenger));
            }
        }*/
//    }


    private void getSalesDetail(String transId) {
        showLoading(true);
        initParamForMposTransaction();
        mposTrans.getTransactionDetail(transId, new MposTransactions.ItfHandlerActionTrans<TransItem>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
                callbackError(dataError);
            }

            @Override
            public void onSuccessActionTrans(TransItem transItem, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
                dataPay = new DataPay(transItem);

                int transactionStatus = transItem.getTransactionDetail().getTransactionStatus();
                saveLRs(ConstantsPay.SALES_HISTORY_DETAIL + " onsuccess transStatus=" + transactionStatus);
                if (transactionStatus == ConstantsPay.TRANS_TYPE_SUCCESS || transactionStatus == ConstantsPay.TRANS_TYPE_PENDING_TC
                        || transactionStatus == ConstantsPay.TRANS_TYPE_PENDING_SIGNATURE) {
                    if (isRunMacq) {
                        voidPayment(transItem.getTransactionDetail().getWfId());
                    }
                    else {
                        voidPayment(transId);
                    }
                }
                else if (transactionStatus == ConstantsPay.TRANS_TYPE_VOID || transactionStatus == ConstantsPay.TRANS_TYPE_REVERSAL) {
                    viewer.onSuccessVoid(dataPrePay, dataPay);
                }
                else {
                    callbackError(new DataError(ERROR_CODE_DEFAULT, getString(R.string.error_void_fail)));
                }
            }
        });
    }

    /**
     *
     * @param transId bank use transId, macq use wfid
     */
    private void voidPayment(String transId) {
        showLoading(true);
        initParamForMposTransaction();
        mposTrans.voidTransaction(transId, new MposTransactions.ItfHandlerActionTrans<Boolean>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
                callbackError(dataError);
            }

            @Override
            public void onSuccessActionTrans(Boolean obj, MposTransactions.ActionTrans typeAction) {
                showLoading(false);
                viewer.onSuccessVoid(dataPrePay, dataPay);
            }
        });
    }

    private void getTransStatus() {
        initParamForMposTransaction();
        mposTrans.getTransactionStatus(dataPrePay.getTxId(), dataPrePay.getUdid(), new MposTransactions.ItfHandlerActionTrans<TransItem>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                // convert error to 2012 without timeout
                if (dataError.getErrorCode()!= ConstantsPay.ERROR_CODE_TIMEOUT_REQ) {
                    dataError.setErrorCode(LibError.ERROR_2012);
                    dataError.setMsg(getString(R.string.ERROR_2012));
                }
                callbackError(dataError);
            }

            @Override
            public void onSuccessActionTrans(TransItem obj, MposTransactions.ActionTrans typeAction) {
                viewer.onSuccessByAction(obj, typeAction);
            }
        });
    }

    private void getListTransWaitSignature() {
        initParamForMposTransaction();
        mposTrans.getListTransactionWaitSignature(new MposTransactions.ItfHandlerActionTrans<TransItem>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactions.ActionTrans typeAction) {
                callbackError(dataError);
            }

            @Override
            public void onSuccessActionTrans(TransItem obj, MposTransactions.ActionTrans typeAction) {
                viewer.onSuccessByAction(obj, typeAction);
            }
        });
    }

    private void initParamForMposTransaction() {
        mposTrans.setRunMacq(isRunMacq);
        mposTrans.setSerialNumber(model.getSerialnumber());
        mposTrans.setUserId(dataPrePay.getMobileUser());
    }

    /**------------------------Update emv-config + firmware ----------------------------------
     **---------------------------------------------------------*/
    DownloadConfigReader downloadConfigReader;
    boolean isUpdateConfigAfterFw = false;
    public void downloadEmvConfig() {
        if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP01 || dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP02) {
            downloadEmvConfigSmartpos();
        } else if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.PAX) {
            downloadEmvConfigPax();
        } else {
            downloadEmvConfigPr02();
        }
    }

    @Override
    public void upgradeFirmware() {
        if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
            downloadFwPr02();
        }
        else if (deviceType == ConstantsPay.DEVICE_P20L) {
            viewer.gotoScreenUpgradeFwSp02();
        }
    }

    private void downloadFwPr02() {
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(context, PrefLibTV.getInstance(context).get(PrefLibTV.urlUpgradeFw, String.class), new DownloadConfigReader.ItfProcessDownloadFw() {
                @Override
                public void showLoading(boolean show) {
                    MposPaymentPresenter.this.showLoading(getString(R.string.downloading_fw));
                }

                @Override
                public void onErrorDownloadFw(String msg) {
                    msg = getString(R.string.error_download_fw, msg);
                    DataError dataError = new DataError(ERROR_CODE_DOWNLOAD_FW, msg);
                    showError(dataError);
                }

                @Override
                public void onSuccessDownloadFw(String pathFile) {
                    startUpgradeFwPr02(pathFile);
                }

                @Override
                public void appendLog(String log) {
                    MposPaymentPresenter.this.appendLog(TypeLog.action, log);
                }
            });
        }
        downloadConfigReader.downloadFw();
    }

    private void startUpgradeFwPr02(String pathFile) {
        isRunningUpgradeFwPr02 = true;
        dspreadControl.setCbUpdateProgress(new LibDspreadReader.ItfResultUpgradeFw() {
            @Override
            public void showProgressUpgradeFw(int progress) {
                viewer.showProgressUpdateFw(progress);
            }

            @Override
            public void showAlertUpgradeFw(int type) {
                appendLogFw("alert: " + type);
                MyDialogShow.showDialogWarning(context, getString(R.string.warning_upgrade_fw_need_charge), getString(R.string.BTN_UPGRADE),
                        view -> dspreadControl.processUpgradeFw(pathFile),
                        view -> callbackError(new DataError(getString(R.string.error_upgrade_fw_pr02)))
//                        view -> checkBeforePay()
                );
            }

            @Override
            public void onSuccessUpgradeFw() {
                isRunningUpgradeFwPr02 = false;
                tickUpdateFwSuccess();
            }

            @Override
            public void onErrorUpgradeFw(QPOSService.UpdateInformationResult arg0) {
                isRunningUpgradeFwPr02 = false;
                if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {
                    MyDialogShow.showDialogError(null, getString(R.string.error_upgrade_fw_pr02, arg0.toString()), context, true,
                            view -> callbackError(new DataError(getString(R.string.error_upgrade_fw_pr02)))
                    );
                }
                else {
                    MyDialogShow.showDialogError(context, getString(R.string.error_upgrade_fw_pr02, arg0.toString()),
                            getString(R.string.PAYMENT_BTN_PAY_NOW), getString(R.string.BTN_CLOSE),
                            view -> checkBeforePay(),
                            view -> callbackError(new DataError(getString(R.string.error_upgrade_fw_pr02)))
                    );
                }
            }
        });
        dspreadControl.processUpgradeFw(pathFile);
    }

    public void downloadEmvConfigPax() {
        appendLogAct("download emvConfig: " + dataPrePay.getReaderType());
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(context, new DownloadConfigReader.ItfProcessDownloadConfigPax() {
                @Override
                public void appendLog(String log) {
                    appendLogEmvConfig(log);
                }

                @Override
                public void showLoading(boolean show) {
                    MposPaymentPresenter.this.showLoading(show);
                }

                @Override
                public void onErrorDownloadConfig(String msg) {
                    showError(new DataError(msg));
                }

                @Override
                public void onSuccessDownloadConfigPax(String config) {
                    saveLAct("onSuccessDownloadConfigPax: " + dataPrePay.getReaderType());
                    Utils.LOGD(TAG, "config= " + config);
                    PrefLibTV.getInstance(context).saveEmvConfigStorage(config);
                    tickUpdateEmvConfigSuccess();
                }
            });
        }
        downloadConfigReader.processDownloadConfig();
    }

    public void downloadEmvConfigSmartpos() {
        appendLogAct("download emvConfig: " + dataPrePay.getReaderType());
        if (downloadConfigReader == null) {
            downloadConfigReader = new DownloadConfigReader(context, new DownloadConfigReader.ItfProcessDownloadConfigSp01() {
                @Override
                public void appendLog(String log) {
                    appendLogEmvConfig(log);
                }

                @Override
                public void showLoading(boolean show) {
                    MposPaymentPresenter.this.showLoading(show);
                }

                @Override
                public void onErrorDownloadConfig(String msg) {
                    showError(new DataError(msg));
                }

                @Override
                public void onSuccessDownloadConfigSp01(String config) {
                    saveLAct("onSuccessDownloadConfigSmartpos: " + dataPrePay.getReaderType());
                    if (dataPrePay.getReaderType() == ConstantsPay.ReaderType.SP02) {
                        handlerDataEmvConfigSp02(config);
                    }
                    else {
                        handlerDataEmvConfigSp01(config);
                    }
                }
            });
        }
        downloadConfigReader.processDownloadConfig();
    }

    private void handlerDataEmvConfigSp02(String content) {
        EmvConfigSp01 emvConfigSp02 = MyGson.parseJson(content, EmvConfigSp01.class);
        if (emvConfigSp02 != null) {
            libKozenP5.processUpdateEmvConfig(emvConfigSp02);
            tickUpdateEmvConfigSuccess();
        }
    }

    private void handlerDataEmvConfigSp01(String content) {
        EmvConfigSp01 emvConfigSp01 = MyGson.parseJson(content, EmvConfigSp01.class);
        if (emvConfigSp01 != null) {
            libP20L.processUpdateConfigSp01(emvConfigSp01);
            tickUpdateEmvConfigSuccess();
        }
    }

    public void downloadEmvConfigPr02() {
        appendLogEmvConfig("start check Url");
        String urlApp = PrefLibTV.getInstance(context).get(PrefLibTV.urlEmvApp, String.class, "");
        String urlCapk = PrefLibTV.getInstance(context).get(PrefLibTV.urlEmvCapk, String.class, "");
        if (TextUtils.isEmpty(urlApp)) {
            urlApp = ConstantsPay.URL_EMV_CONFIG_PR02_APP;
            appendLogEmvConfig("use default UrlApp");
        }
        if (TextUtils.isEmpty(urlCapk)) {
            urlCapk = ConstantsPay.URL_EMV_CONFIG_PR02_CAPK;
            appendLogEmvConfig("use default UrlCapk");
        }
        if (!TextUtils.isEmpty(urlApp) && !TextUtils.isEmpty(urlCapk)) {

            if (downloadConfigReader == null || isUpdateConfigAfterFw) {
                downloadConfigReader = new DownloadConfigReader(context, urlApp, urlCapk, new DownloadConfigReader.ItfProcessDownloadConfig() {
                    @Override
                    public void appendLog(String log) {
                        appendLogEmvConfig(log);
                    }

                    @Override
                    public void showLoading(boolean show, String msg) {
                        viewer.showLoading(msg, show);
                    }

                    @Override
                    public void onSuccessDownloadConfig(String emvConfigApp, String emvConfigCapk) {
                        connectToPr02UpdateConfig(emvConfigApp, emvConfigCapk);
                    }

                    @Override
                    public void onErrorDownloadConfig(int result, String msg) {
                        showError(new DataError(msg));
                    }
                });
            }
            downloadConfigReader.processDownloadConfig();
        }
        else {
            appendLogEmvConfig("not found url:"+(TextUtils.isEmpty(urlApp)?"EmvApp ":" ") + (TextUtils.isEmpty(urlCapk)?"Capk":""));
            showError(new DataError("Can not found url update EC"));
        }
    }

    private void appendLogFw(String log) {
        appendLog(TypeLog.action,"U_FW " + log );
    }
    private void appendLogEmvConfig(String log) {
        appendLog(TypeLog.action,"U_EMV_CONFIG " + log );
    }

    private final int ACT_TYPE_UPDATE_CONFIG    = 0;
    private final int ACT_TYPE_INJECT_KEY       = 1;
    private void connectToPr02UpdateConfig(String emvConfigApp, String emvConfigCapk) {
        connectPr02ToActionByType(ACT_TYPE_UPDATE_CONFIG, emvConfigApp, emvConfigCapk);
    }

    private void connectPr02ToInjectKey() {
        connectPr02ToActionByType(ACT_TYPE_INJECT_KEY, null, null);
    }
    private void connectPr02ToActionByType(int type, String emvConfigApp, String emvConfigCapk) {
        appendLogEmvConfig("connect to reader");
        showLoading(getString(R.string.msg_connecting_device_please_wait, model.getSerialnumber()));

        dspreadControl.setTypeController(LibDspreadReader.TYPE_CONTROLLER_GET_SERIAL_NUMBER);
        if (type == ACT_TYPE_UPDATE_CONFIG) {
            dspreadControl.setCbUpdateConfig((isSuccess, result) -> {
                appendLogEmvConfig("result updateEmvConfig=" + isSuccess + " ->" + result);
                hideLoading();
                if (isSuccess) {
                    isUpdateConfigAfterFw = false;
                    tickUpdateEmvConfigSuccess();
                } else {
                    MyDialogShow.showDialogError(context, getString(R.string.update_config_reader_fail), getString(R.string.ALERT_BTN_RETRY), getString(R.string.BTN_CLOSE),
                            view -> downloadEmvConfig(), v -> callbackError(new DataError(getString(R.string.update_config_reader_fail))));
                }
            });
        }
        dspreadControl.setCallBackConnectDevice(new LibDspreadReader.ResultConnectDspread() {
            @Override
            public void onSuccessConnectDevice(BluetoothReaderPair dspreadDevicePair) {
                startCallReaderUpdateConfig(type, emvConfigApp, emvConfigCapk);
            }

            @Override
            public void onFailConnectDevice(BluetoothReaderPair dspreadDevicePair) {
                Utils.LOGD(TAG, "onFailConnectDevice: ");
                hideLoading();
                appendLogEmvConfig("fail connect device");
                MyDialogShow.showDialogRetry(getString(R.string.error_not_connect_reader),
                        context, view -> connectPr02ToActionByType(type, emvConfigApp, emvConfigCapk));
            }

            @Override
            public void foundDevices(ArrayList<BluetoothDevice> arrayList) {

            }
        });
        if (dspreadControl.getPos() != null && dspreadControl.getPos().isIdle() && !isUpdateConfigAfterFw) {
            Utils.LOGD(TAG, "connectToPr02ActionByType: ===>111");
            startCallReaderUpdateConfig(type, emvConfigApp, emvConfigCapk);
        }
        else {
            Utils.LOGD(TAG, "connectToPr02ActionByType: ===>222");
            dspreadControl.initPinpadStartTransaction();
        }
    }

    private void startCallReaderUpdateConfig(int type, String emvConfigApp, String emvConfigCapk) {
        if (type == ACT_TYPE_UPDATE_CONFIG) {
            hideLoading();
            showLoading(getString(R.string.updating_config_reader));
            appendLogEmvConfig("start update emv config");
            dspreadControl.getPos().updateEmvConfig(emvConfigApp, emvConfigCapk);
        }
        else {
            hideLoading();
            appendLogEmvConfig("connected PR02");
            AppExecutors.getInstance().mainThread().execute(this::processGetIpek);
        }
    }

    private void processGetIpek() {
        if (libInjectKey != null) {
            if (isRunMacq) {
                libInjectKey.getIpekFromMA();
            }
            else {
                libInjectKey.processInjectKey();
            }
        }
    }

    public void tickUpdateFwSuccess() {

        appendLogApi(ConstantsPay.SET_FIRMWARE_UPDATE);
        StringEntity entity = null;
        try {
            String versionUpgraded;
            if (deviceType == ConstantsPay.DEVICE_P20L) {
                versionUpgraded = libP20L.getSmartPosApi().getDeviceVersion().getSoftVersion();
            }
            else {
                versionUpgraded = PrefLibTV.getInstance(context).get(PrefLibTV.versionFwUpgrade, String.class, "");
            }
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.SET_FIRMWARE_UPDATE);
            jo.put("readerSerial", model.getSerialnumber());
            jo.put("isFirmwareUpdate", true);
            jo.put("platform", ConstantsPay.PLATFORM);

            jo.put("versionFirmwareUpdate", versionUpgraded);
            Utils.LOGD(TAG, "SET_FIRMWARE_UPDATE Data: "+ jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "tickUpdateFwSuccess: "+ e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_GATEWAY, entity,
                ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showLoading(true);
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        hideLoading();
                        String msgError;

                        JsonParser jsonParser = new JsonParser();
                        String content = new String(arg2);
                        BaseObjJson errorBean = jsonParser.checkHaveError(content);
                        Utils.LOGD(TAG, "SET_FIRMWARE_UPDATE onSuccess: "+content);
                        if (Constants.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
                            PrefLibTV.getInstance(context).put(PrefLibTV.upgradeFw, false);
                            handlerTickUpdateSuccess(ConstantsPay.SET_FIRMWARE_UPDATE);
                            return;
                        }
                        else {
                            msgError = getString(R.string.error_tick_update_fw) + "(" + errorBean.code + " " + errorBean.message + ")";
                        }

                        if (!TextUtils.isEmpty(msgError)) {
                            showDialogErrorContinuePayOrRetry(msgError, v -> tickUpdateFwSuccess());
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        hideLoading();
                        Utils.LOGE(TAG, "SET_FIRMWARE_UPDATE Error: ", arg3);
                        appendLogApi(ConstantsPay.SET_FIRMWARE_UPDATE+" error: request time out");
                        showDialogErrorContinuePayOrRetry(getString(R.string.error_tick_update_fw), v -> tickUpdateFwSuccess());
                    }
                });
    }

    public void tickUpdateEmvConfigSuccess() {

        appendLogApi(ConstantsPay.UPGRADE_EMV_CONFIG_SUCCESS);
        PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, false);
        PrefLibTV.getInstance(context).put(PrefLibTV.lastTimeUpgradeEMVConfig, System.currentTimeMillis());
        PrefLibTV.getInstance(context).put(PrefLibTV.retryUpdateEmvConfig, false);

        StringEntity entity = null;
        try {
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, ConstantsPay.UPGRADE_EMV_CONFIG_SUCCESS);
            jo.put("readerSerial", model.getSerialnumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", dataPrePay.getMobileUser());
            Utils.LOGD(TAG, "UPGRADE_EMV_CONFIG_SUCCESS Data: "+ jo);
            entity = new StringEntity(jo.toString());
        } catch (Exception e1) {
            Utils.LOGE(TAG, "updateCAKey: "+ e1.getMessage());
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.URL_MPOS_GATEWAY, entity,
                ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showLoading(true);
                    }

                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                        hideLoading();
                        String msgError = null;
                        JsonParser jsonParser = new JsonParser();
                        String content = new String(arg2);
                        BaseObjJson errorBean = jsonParser.checkHaveError(content);

                        Utils.LOGD(TAG, "UPGRADE_EMV_CONFIG_SUCCESS onSuccess: "+content);
                        if (Constants.CODE_REQUEST_SUCCESS.equals(errorBean.code)) {
//                            PrefLibTV.getInstance(context).put(PrefLibTV.upgradeEMVConfig, false);
                            handlerTickUpdateSuccess(ConstantsPay.UPGRADE_EMV_CONFIG_SUCCESS);
                        }
                        else {
                            msgError = getString(R.string.error_tick_update_config) + "(" + errorBean.code + " " + errorBean.message + ")";
                        }

                        if (!TextUtils.isEmpty(msgError)) {
                            showDialogErrorContinuePayOrRetry(msgError, v -> tickUpdateEmvConfigSuccess());
                        }
                    }

                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                        hideLoading();
                        Utils.LOGE(TAG, "confirm payment Error: ", arg3);
                        appendLogApi(ConstantsPay.UPGRADE_EMV_CONFIG_SUCCESS+" error: request time out");
                        showDialogErrorContinuePayOrRetry(getString(R.string.error_tick_update_fw), v -> tickUpdateEmvConfigSuccess());
                    }
                });
    }

    @Override
    public void saveNumSaleRemain(int num) {
        PrefLibTV.getInstance(context).put(PrefLibTV.numSaleRemain, num);
    }

    private void handlerTickUpdateSuccess(String typeService) {
        saveNumSaleRemain(Constants.NUM_SALE_REMAIN_DEFAULT);
        DataCache.getInstance().clearCache();
        PrefLibTV.getInstance(context).setMcConfigCache("");
//        PrefLibTV.getInstance(context).put(PrefLibTV.DATA_MERCHANT_CONFIG_CACHE_SDK, "");
        if (deviceType == ConstantsPay.DEVICE_P20L  && typeService.equals(ConstantsPay.SET_FIRMWARE_UPDATE)) {
            sl();
            showRebootDialog();
            return;
        }
        else if (deviceType == ConstantsPay.DEVICE_DSPREAD  && typeService.equals(ConstantsPay.SET_FIRMWARE_UPDATE)) {
            // need update emv-config
            isUpdateConfigAfterFw = true;
            // wait 10s for reader ready for connect
            Handler handler = new Handler();
            handler.postDelayed(this::downloadEmvConfigPr02, 10000);
            return;
        }

        if (MposIntegrationHelper.A_UPDATE_POS_CONFIG.equals(dataPrePay.getActionType())) {
            MyDialogShow.showDialogInfo(context, getString(R.string.update_config_reader_success), true,
                    getString(R.string.BTN_OK), v -> callbackUpdateSuccessConfig());
        }
        else {
            showDialogSuccessContinuePayOrExit(getString(R.string.update_config_reader_success));
        }

    }

    private void showRebootDialog() {
        new AlertDialog.Builder(context)
                .setTitle(R.string.reboot)
                .setMessage(R.string.reboot_msg)
                .setPositiveButton(R.string.yes, (dialogInterface, i) -> libP20L.getSmartPosApi().reboot())
                .setCancelable(false)
                .create()
                .show();
    }

    private void showDialogSuccessContinuePayOrExit(String msg) {
        MyDialogShow.showDialogInfo(context, msg, true, getString(R.string.PAYMENT_BTN_PAY_NOW), getString(R.string.BTN_CLOSE), v -> checkBeforePay(), v ->{});
    }
    private void showDialogErrorContinuePayOrRetry(String msg, View.OnClickListener clickRetry) {
        MyDialogShow.showDialogRetryCancel(null, msg, context, clickRetry, v -> checkBeforePay(),true);
    }

    // auto inject
    private String readerInjected;
    private boolean runCheckAutoInject = false;

    private boolean checkNeedAutoInjectKey() {
        runCheckAutoInject = true;
        readerInjected = PrefLibTV.getInstance(context).get(PrefLibTV.readersInjected, String.class, "");
        libInjectKey = new LibInjectKey(context, deviceType,
                isRunMacq ? ConstantsPay.SERVER_MPOS_ACQUIRER : PrefLibTV.getInstance(context).getFlagServer(),
                model.getSerialnumber(), libP20L);
        libInjectKey.setCbLog((typeLog, s) -> appendLogAct(s));
        libInjectKey.setCallbackShowLoading(this::showLoading);

        if (libInjectKey.checkNeedAutoInjectKey(readerInjected, isRunMacq, model.getSerialnumber(), currBankName)) {
            processInject();
            return true;
        }
        return false;
    }

    private void handlerFinishInject(int type, int res, String s) {
        if (type == LibInjectKey.TYPE_KEY_END || type == LibInjectKey.TYPE_KEY_NONE) {
            if (res == LibInjectKey.CODE_INJECT_SUCCESS) {
                saveInjected();
            }
            if (dspreadControl != null) {
                dspreadControl.setCbResultInject(null);
            }
            checkBeforePay();
        }
    }

    private void saveInjected() {
        libInjectKey.saveInjected(readerInjected, currBankName);
    }

    private void processInject() {
        Utils.LOGD(TAG, "processInject -->" + (isRunMacq ? "macq" : "bank") + " ->" + deviceType);
        libInjectKey.setCallback((type, res, s) -> {
            Utils.LOGD(TAG, "processInjectBank: type="+type+" res="+res+" ->"+s);
            handlerFinishInject(type, res, s);

        });
        if (deviceType == ConstantsPay.DEVICE_PAX) {
            libInjectKey.setLibPax(libPax);
        }

        if (deviceType == ConstantsPay.DEVICE_DSPREAD) {
            connectPr02ToInjectKey();
            libInjectKey.setDspreadControl(dspreadControl);
        } else if (isRunMacq) {
            appendLogInjectKey("start get Ipek MA");
            libInjectKey.getIpekFromMA();
        } else {
            appendLogInjectKey("start get Ipek bank");
            libInjectKey.processInjectKey();
        }
    }


    //---------------------------------------- CALLBACK --------------------------------------------------
    private void callbackUpdateSuccessConfig() {
        viewer.callbackToPartner(STATUS_UPDATED, null, null, null);
    }
    private void callbackError(DataError dataError) {
        viewer.callbackToPartner(TRANS_STATUS_ERROR, dataError, dataPrePay, dataPay);
    }

    private void showError(DataError dataError) {
        if (dataPay == null && dataPrePay!= null) {
            dataPay = new DataPay(dataPrePay);
        }
        sendErrorToMpos(dataPay, dataError);
        viewer.showError(dataError);
    }

    private void saveLAct(String text) {
        sv.appendLogAction(text);
    }

    private void saveLRs(String text) {
        sv.appendLogResponse(text);
    }

    public void appendLogAct(String log) {
        sv.appendLog(TypeLog.action, log);
    }

    public void appendLogApi(String log) {
        sv.appendLog(TypeLog.request, log);
    }

    private void appendLogInjectKey(String log) {
        Utils.LOGD(TAG, "appendLogInjectKey: " + log);
        sv.appendLogAction("IJ:  " + log);
    }

    @Override
    public void appendLog(TypeLog type, String log) {
        sv.appendLog(type, log);
    }
}
