package com.mpos.sdk.core.control;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentManager;

import com.custom.mdm.CustomAPI;
import com.google.gson.JsonObject;
import com.loopj.android.http.AsyncHttpResponseHandler;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.R;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.DataPayEmart;
import com.mpos.sdk.core.model.DataPrePay;
import com.mpos.sdk.core.model.DataProcessCard;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.LibError;
import com.mpos.sdk.core.model.MposCustom;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.*;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.mposinterface.ResultProcessCard;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.AppExecutors;
import com.mpos.sdk.util.CardUtils;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.MyDialogShow;
import com.mpos.sdk.util.MyOnClickListenerView;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.PayUtils;
import com.mpos.sdk.util.ScreenUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.BottomSheetPromotion;
import com.mpos.sdk.view.BottomSheetPromotionFurther;
import com.mpos.sdk.view.DialogBaseSignature;
import com.mpos.sdk.view.DialogNewSignature;
import com.mpos.sdk.view.DialogSignature;
import com.mpos.sdk.view.SelectApplicationAdapter;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;

import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_FAIL_CHIP_AID;
import static com.mpos.sdk.util.ConstantsPay.ERROR_CODE_MA_DEFAULT;
import static com.mpos.sdk.util.ConstantsPay.TRX_TYPE_SERVICE;

public abstract class LibReaderController extends LibReaderConfig implements LibLoginHandler.ItfHandlerResultLogin {

    static String TAG = "LibReaderController";

    protected static final int POSITION_KEY_STB    = 0;
    protected static final int POSITION_KEY_BIDV   = 1;
    protected static final int POSITION_KEY_VTB    = 2;
    protected static final int POSITION_KEY_PVCB   = 3;
    protected static final int POSITION_KEY_OPEN99 = 4;
    protected static final int POSITION_KEY_VCB    = 5;
    protected static final int POSITION_KEY_TCB    = 6;
//    public static final int POSITION_KEY_MPOS   = 5;
//    public static final int POSITION_KEY_MAILINH= 5;
    protected static final int POSITION_KEY_NEXTPAY    = 9;


    public static final int TYPE_PROCESS_NORMAL         = 0;
    public static final int TYPE_PROCESS_GET_CARD       = 1;
    public static final int TYPE_PROCESS_GET_CARD_PIN   = 2;
    public static final int TYPE_PROCESS_CHANGE_PIN     = 3;


    /* type control of lib */
    public static final int TYPE_CONTROLLER_GET_SERIAL_NUMBER = 1;
    public static final int TYPE_CONTROLLER_SWIPE_CARD = 2;


    private MposCustom sdkCustom;

    // info show in dialog result
    private String moreInfoOfTrans;

    private boolean useNewSignature = false;
    private boolean autoCheckWaitSignWhenTimeOut = false;
    protected boolean autoSelectApp = false;
    protected boolean checkMaskPan = false;
    protected boolean autoSelectAppNapas = false;
    protected boolean hasRunCheckSignature = false;
    protected int typeController  = TYPE_CONTROLLER_SWIPE_CARD;
    protected int typeProcessCard = TYPE_PROCESS_NORMAL;

    protected ResultProcessCard cbProcessCard;


    protected Context context;

    protected DataPrePay dataPrePay;
    protected DataPay dataPayCache;
    protected String amount = "0", udid, trxType;
    protected String pan = "", holderName = "", txId = "";//approvalCode = "",
    protected String maskedPan = "";
    protected String wk;
    protected String currency = ConstantsPay.CURRENCY_SPACE_PRE;
    protected String nameTypeCard;
    protected String base64Signature;
    protected long timeStartCallPay;
    protected long timePayment;
    long lAmount;

    protected String defaultAppNameNapasSelected = "napas";
//    String defaultAppNameNapasSelected = "A1040007A000000727101000";

    int currStageProcess = STAGE_PROCESS_NONE;

    private boolean isLibHandlerSignature = true;
    private boolean showErrorCodeInMsg = true;
    private boolean dismissSignature = false;

    private boolean isSaveInstanceState = false;
    private boolean callInitPresale = false;

    protected JSONObject joMoreDetailSale;

    private LibSignature libSignature;

    private ItfResultPay cbResultPay;
    private ItfAppendLog cbSaveLog;
    protected ItfCheckMaskPan itfCheckMaskPan;
    protected ItfCertifyMacq itfCertifyMacq;

    protected PreSaleRes preSaleConfig;

    protected boolean isRunMacq = false;
    protected boolean isEnterPinOk = false;
    protected boolean isErrorCodeToPos = false;

    protected DataSaleRes dataSaleSuccess;
//    protected PromotionInfo promotionInfo;
    protected String encDataMag;
    protected String encTrack1;
    protected String encTrack2;
    protected String ksn;

    protected JSONObject joApproval;
    protected JSONObject joRootEmvSale;
    protected String emvScript = "";
    protected String mDataEmv = "";
    protected boolean isContactLess = false;
    protected Boolean isRestrictNfc = null;
    protected boolean isFinishedPayment = false;
    protected boolean isOpenAppAgain = false; // sending msg sale -> press home -> open app

    protected LibDspreadReader.ItfUpdateViewDspread cbUpdateUI;

    private DialogBaseSignature dialogSignature;
    private ItfHandleBeforeSignature itfHandleBeforeSignature;
    private ItfHandleActionTracking itfHandleActionTracking;
    private ItfHandlePromotion itfHandlePromotion;
//    private ItfHandleMcp itfHandleMcp;


    private static final String INVALID_CARD_NUMBER = "INVALID_CARD_NUMBER";
    private static final String INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE";

    public LibReaderController(Context c){
        this.context = c;
    }
    public LibReaderController(Context c, DataPrePay dataPrePay){
        this.context = c;
        this.dataPrePay = dataPrePay;
    }

    abstract String getReaderType();
    abstract void handleSuccessSaleMacq(DataSaleSend dataSend, DataSaleRes data);
    abstract void handleWorkingKeyFromServer(String ezpk);
    abstract void processAlertRemoveCard();

    abstract void sendDenialToTerminal();
    abstract void runScriptEMV(String emvScript);
    abstract void sendErrorCodeToPos(String codeError, String emvScript);

    abstract void startReadCard();
    abstract void closeReadCard();
    abstract void cancelReadCard();

    protected void initVariable(ItfResultPay callback) {
        getAmountPay();

        if (dataPrePay != null) {
            udid = dataPrePay.getUdid();
            trxType = dataPrePay.getTrxType();
            isLibHandlerSignature = dataPrePay.isLibHandlerSignature();   // app mpos: default not send extra
        }

        setCallback(callback);

        if (TextUtils.isEmpty(udid)) {
            udid = Utils.zenUdid();
        }
        if (TextUtils.isEmpty(trxType)) {
            trxType = ConstantsPay.TRX_TYPE_NORMAL;
        }

        if (Utils.checkTypeBuildIsCertify()) {
            testNapas = true;
            testAmex = true;
            enableFallback = true;
            autoSelectAppNapas = true;
            // vpb app is not fake amount
            if (PrefLibTV.getInstance(context).get(PrefLibTV.fAmtCertify, Boolean.class, false)) {
                fakeAmount = true;
            }
        }
        SoundEffects.getInstance().init(context);
    }

    protected void initPresale() {
        callInitPresale = true;
        int flagServer = PrefLibTV.getInstance(context).getFlagServer();
        Utils.LOGD(TAG, "initVariable: flagServer=" + flagServer);
        if (flagServer == ConstantsPay.SERVER_MPOS_ACQUIRER) {
            isRunMacq = true;
        }
        if (isRunMacq) {
            String preSaleSave = PrefLibTV.getInstance(context).get(PrefLibTV.PRE_SALE_CONFIG, String.class);
            Utils.LOGD(TAG, "initVariable: "+preSaleSave);
            preSaleConfig = MyGson.parseJson(preSaleSave, PreSaleRes.class);
            if (preSaleConfig == null) {
                preSaleConfig = new PreSaleRes();
            }
        }
        appendLogAction("init presale: " + flagServer + " isMacq=" + isRunMacq
                + " magAcq=" + (preSaleConfig == null ? "" : preSaleConfig.getAcquirerMagstripe()));
    }

    void getAmountPay() {
        Utils.LOGD(TAG, "getAmountPay " + dataPrePay.getAmount());
        lAmount = dataPrePay != null ? dataPrePay.getAmount() : 0;
        amount = String.valueOf(lAmount);

        Utils.LOGD(TAG, "getAmountPay lAmount: " + lAmount);
        Utils.LOGD(TAG, "getAmountPay amount: " + amount);

    }

    public void setTypeProcessCard(int typeProcessCard) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.typeProcessCard = typeProcessCard;
        }
    }

    private void processReSaleWithPromotion(ArrayList<PromotionInfo> arrSelectedPromotions, String newAmount) {
        amount = newAmount;
        this.arrPromotionsSelected = arrSelectedPromotions;
        if (itfHandlePromotion != null) {
//            itfHandlePromotion.onHandlePromotion(arrSelectedPromotions, Long.parseLong(amount));
            itfHandlePromotion.onHandlePromotion(Long.parseLong(amount));
        }
        reSaleAgain = true;
        initVariableNewPayment();
        resetDataPayCache();
        // if device is dspread -> need delay 1s to close previous transaction
        if (getReaderType().equals(ConstantsPay.READER_TYPE_DSPREAD)) {
            cancelReadCard();
            new Handler().postDelayed(this::startPayment, 1000);
        }
        else {
            startPayment();
        }
    }

    public void setItfHandlePromotion(ItfHandlePromotion handlePromotion) {
        this.itfHandlePromotion = handlePromotion;
    }
//    public void setItfHandleMcp(ItfHandleMcp handleMcp) {
//        this.itfHandleMcp = handleMcp;
//    }
    public void setCbProcessCard(ResultProcessCard cbProcessCard) {
        this.cbProcessCard = cbProcessCard;
    }

    public void setCallback(ItfResultPay callback) {
        cbResultPay = callback;
    }

    public void setCallBackSaveLog(ItfAppendLog cb) {
        this.cbSaveLog = cb;
    }

    public int getCurrStageProcess() {
        return currStageProcess;
    }

    protected String getString(int resId) {
        return context != null ? context.getString(resId) : "";
    }

    protected String getString(int resId, String... arrParams) {
        return context != null ? context.getString(resId, arrParams==null? new Object[]{""} :arrParams) : "";
    }

    protected void initMaskedPan(String maskedPan) {
        if (!TextUtils.isEmpty(maskedPan)) {
            try {
                CardUtils cardUtils = new CardUtils();
                this.maskedPan = cardUtils.removeLastNotDigit(maskedPan);
                if (!this.maskedPan.equals(maskedPan)) {
                    appendLogAction("maskPan: " + maskedPan+" -> " + this.maskedPan);
                }
            } catch (Exception e) {
                appendLogAction("error removing last not digit:"+ e);
            }
        } else {
            this.maskedPan = "";
        }
    }

    protected String getNameTypeProcessCard(){
        switch (typeProcessCard) {
            case TYPE_PROCESS_NORMAL:
                return "normal";
            case TYPE_PROCESS_GET_CARD:
                return "read card";
            default:
                return "not define";
        }
    }

    public int getTypeController() {
        return typeController;
    }

    public void setSdkCustom(MposCustom sdkCustom) {
        this.sdkCustom = sdkCustom;
    }

    public void setAutoCheckWaitSignWhenTimeOut(boolean autoCheckWaitSignWhenTimeOut) {
        this.autoCheckWaitSignWhenTimeOut = autoCheckWaitSignWhenTimeOut;
    }

    public void setAutoSelectAppNapas(boolean autoSelectAppNapas) {
        this.autoSelectAppNapas = autoSelectAppNapas;
    }

    public void setLibHandlerSignature(boolean libHandlerSignature) {
        isLibHandlerSignature = libHandlerSignature;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setMoreInfoOfTrans(String moreInfoOfTrans) {
        this.moreInfoOfTrans = moreInfoOfTrans;
    }

    public void setShowErrorCodeInMsg(boolean showErrorCodeInMsg) {
        this.showErrorCodeInMsg = showErrorCodeInMsg;
    }

    public void setUseNewSignature(boolean useNewSignature) {
        this.useNewSignature = useNewSignature;
    }

    public void setAutoSelectApp(boolean autoSelectApp) {
        this.autoSelectApp = autoSelectApp;
    }

    public void setJoMoreDetailSale(JSONObject joMoreDetailSale) {
        this.joMoreDetailSale = joMoreDetailSale;
    }

    public void setCheckMaskPan(boolean checkMaskPan) {
        this.checkMaskPan = checkMaskPan;
    }

    public void setEnableInjectKey(boolean enableInjectKey) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.enableInjectKey = enableInjectKey;
        }
    }
    public void setItfCheckMaskPan(ItfCheckMaskPan itfCheckMaskPan) {
        this.itfCheckMaskPan = itfCheckMaskPan;
    }

    public void setDismissSignature(boolean dismissSignature) {
        this.dismissSignature = dismissSignature;
    }

    public void setItfHandleBeforeSignature(ItfHandleBeforeSignature itfHandleBeforeSignature) {
        this.itfHandleBeforeSignature = itfHandleBeforeSignature;
    }

    public void setCbUpdateUI(LibDspreadReader.ItfUpdateViewDspread cbUpdateUI) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.cbUpdateUI = cbUpdateUI;
        }
    }

    public void setItfHandleActionTracking(ItfHandleActionTracking itfHandleActionTracking) {
        if (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) {
            this.itfHandleActionTracking = itfHandleActionTracking;
        }
    }

    public void setOpenAppAgain(boolean openAppAgain) {
        this.isOpenAppAgain = openAppAgain;
    }

    public void resetDataPayCache() {
        Utils.LOGD(TAG, "resetDataPayCache: *********");
        isSaveInstanceState = false;
        dataPayCache = null;
    }

    protected void initVariableNewPayment() {
        currStageProcess = STAGE_PROCESS_START;
        hasRunCheckSignature = false;
        isEnterPinOk = false;
        isErrorCodeToPos = false;
        isFinishedPayment = false;
        isOpenAppAgain = false;
        isRestrictNfc = null;
    }

    void runOnUiThread(Runnable runnable) {
        if (runnable != null) {
            AppExecutors.getInstance().mainThread().execute(runnable);
        }
    }

    void finish(int typeErrorFinish) {
        if (cbResultPay != null) {
            cbResultPay.onFinishWithTypeError(typeErrorFinish);
        }
    }

    void addAmountTrxTypeToRequest(JSONObject jo) throws JSONException {
        if (trxType.equals(ConstantsPay.TRX_TYPE_SERVICE)) {
            jo.put("amountDomestic", dataPrePay != null ? dataPrePay.getAmountDomestic() : "");
            jo.put("amountInternational", dataPrePay != null ? dataPrePay.getAmountInternational() : "");
        }
        jo.put("trxType", trxType);
    }

    protected void changeAmountServiceMacq() {
        appendLogRequest("changeAmount: trxType= " + trxType);
        if (trxType.equals(TRX_TYPE_SERVICE) && !TextUtils.isEmpty(maskedPan) && dataPrePay != null) {
            try {
                if (maskedPan.startsWith("9704")) {
                    if (dataPrePay.getAmountDomestic() > 0) {
                        amount = String.valueOf(dataPrePay.getAmountDomestic());
                    }
                }
                else {
                    if (dataPrePay.getAmountInternational() > 0) {
                        amount = String.valueOf(dataPrePay.getAmountInternational());
                    }
                }
                appendLogRequest("amount change to: " + amount);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    void addMoreFieldSale(JSONObject jsonRoot) {
        if (joMoreDetailSale != null) {
            Iterator<String> keys = joMoreDetailSale.keys();
            while (keys.hasNext()) {
                try {
                    String key = keys.next();
                    jsonRoot.put(key, joMoreDetailSale.get(key));
                    Utils.LOGD(TAG, "addMoreFieldSale: -->key="+key+" value="+joMoreDetailSale.get(key));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    protected void setNameTypeCard(String nameTypeCard) {
        this.nameTypeCard = nameTypeCard;
        showNotifyDetectCard();
    }

    void initMagstripeSale() {
        nameTypeCard = ConstantsPay.CARD_MAGSTRIPE;
        initMsgSale();
    }

    void initEmvSale() {
        initEmvSale(false);
    }

    void initEmvSale(boolean isContactLess) {
        nameTypeCard = isContactLess ? ConstantsPay.CARD_NFC : ConstantsPay.CARD_EMV;
        initMsgSale();
    }

    private void initMsgSale() {
        appendLogAction("nameTypeCard=" + nameTypeCard);
        setTimeStartCallPay();
        PrefLibTV.getInstance(context).setLastErrorSale(TYPE_ERROR_DEFAULT);
    }

    protected void setTimeStartCallPay() {
        this.timeStartCallPay = System.currentTimeMillis();
    }

    void calculatorTimePayment() {
        timePayment = (System.currentTimeMillis() - timeStartCallPay)/2;
        Utils.LOGD(TAG, "calculatorTimePayment:  curr-->"+System.currentTimeMillis());
        Utils.LOGD(TAG, "calculatorTimePayment: start-->"+timeStartCallPay);
        Utils.LOGD(TAG, "calculatorTimePayment:  time-->"+timePayment);
    }

    protected String getWorkingKey() {
        String wk;
        if (isRunMacq) {
            wk = preSaleConfig.geteZPKDomestic();
        }
        else {
            wk = PrefLibTV.getInstance(context).getWorkingKey();
        }
        wk = MyTextUtils.removeSpace(wk);

        if (wk.length() > 32) {
            wk = wk.substring(wk.length() - 32);
        }
        return wk;
    }

    protected int getTypeServer() {
        if (isRunMacq) {
            return ConstantsPay.getTypeServerByBanKName(preSaleConfig.getAcquirerMagstripe());
        }
        else {
            return PrefLibTV.getInstance(context).getFlagServer();
        }
    }

    public int getKeyIndexByFlagServer(int typeServer) {
        int keyDataPosition = -1;
        Utils.LOGD(TAG, "getKeyDataIndex: typeServer=" + typeServer);
        if (typeServer == ConstantsPay.SERVER_SCB) {
            keyDataPosition = POSITION_KEY_STB;
        }
        else if (typeServer == ConstantsPay.SERVER_BIDV) {
            keyDataPosition = POSITION_KEY_BIDV;
        }
        else if (typeServer == ConstantsPay.SERVER_VTB) {
            keyDataPosition = POSITION_KEY_VTB;
        }
        else if (typeServer == ConstantsPay.SERVER_OPEN99) {
            keyDataPosition = POSITION_KEY_OPEN99;
        }
        else if (typeServer == ConstantsPay.SERVER_VCB) {
            keyDataPosition = POSITION_KEY_VCB;
        }
        else if (typeServer == ConstantsPay.SERVER_TCB) {
            keyDataPosition = POSITION_KEY_TCB;
        }
        else if (typeServer == ConstantsPay.SERVER_NEXTPAY) {
            keyDataPosition = LibDspreadReader.POSITION_KEY_NEXTPAY;
        }
        appendLogAction("isMA: " + isRunMacq + " flagS=" + typeServer + " index=" + keyDataPosition);
        Utils.LOGD(TAG, "getKeyDataIndex: index=" + keyDataPosition);
        return keyDataPosition;
    }

    protected String parseIssuerScript(String scriptOrigin) {
        String scriptRun = "";
        if (!TextUtils.isEmpty(scriptOrigin)) {
            scriptOrigin = scriptOrigin.substring(1, scriptOrigin.length() - 1);
            String[] expressions = scriptOrigin.split(",");
            String tag91 = "";
            String tag71 = "";
            String tag72 = "";
            String tempKey, tempValue;
            for (String exp : expressions) {
                try {
                    tempKey = exp.split("=")[0].trim();
                    tempValue = exp.split("=")[1];
                    switch (tempKey) {
                        case "KEY_RAW_ARRAY_71":
                            appendLogAction("->71:");
                            tag71 = tempValue.substring(1, tempValue.length() - 1);
                            break;
                        case "KEY_RAW_ARRAY_72":
                            appendLogAction("->72:");
                            tag72 = tempValue.substring(1, tempValue.length() - 1);
                            break;
                        // edit anhnt
                        case "KEY_RAW_ARRAY_91":
//                        else if(tempKey.equals("91")){
                            appendLogAction("->91:");
                            tag91 = tempValue.substring(1, tempValue.length() - 1);
//                            tag91 = tempValue;//.substring(1, exp.split("=")[1].length() - 1);
                            Utils.LOGD(TAG, "tag91:" + tag91);
                            break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            scriptRun = tag71 + tag72 + tag91;
        }
        Utils.LOGD(TAG, "parseScriptEmv: scriptRun="+scriptRun);
        return scriptRun;
    }

    boolean checkCanContinuePay() {
        if (checkMaskPan && itfCheckMaskPan != null) {

            if (TextUtils.isEmpty(maskedPan) || itfCheckMaskPan.checkMaskPanCanPay(maskedPan)) {
                return true;
            } else {
                appendLogRequest("maskP can not continue pay");
                return false;
            }
        }
        else {
            return true;
        }
    }

    public boolean checkRestrictionNfc() {
        if (isRestrictNfc == null) {
            boolean restrictionNFC = PrefLibTV.getInstance(context).get(PrefLibTV.restrictionNFC, Boolean.class, false);
            if (restrictionNFC) {
                isRestrictNfc = true;
            } else {
                isRestrictNfc = checkAmountMoreThanNfcCvmLimit();
            }
        }
        return isRestrictNfc;
    }
    boolean checkAmountMoreThanNfcCvmLimit() {
        long nfcCvmLimit = PrefLibTV.getInstance(context).get(PrefLibTV.amountNfcCvmLimit, Long.class, 0L);
        if (nfcCvmLimit > 0 && lAmount >= nfcCvmLimit) {
            appendLogAction("Amount more than CVM limit(" + nfcCvmLimit + ")");
            return true;
        }
        return false;
    }

    private AsyncStartPayment asyncStartPayment;
    public void startPayment() {
        cancelAsyncPayment();
        if (DevicesUtil.isP20L() || DevicesUtil.isSP02()) {
            Utils.LOGD(TAG, "startPaymentWitAsyncTask: -->");
            asyncStartPayment = new AsyncStartPayment(this);
            asyncStartPayment.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
        }
        else {
            typeController = TYPE_CONTROLLER_SWIPE_CARD;
            if (typeProcessCard == TYPE_PROCESS_GET_CARD) {
                amount = "1";
            }
//            if (reSaleAgain) {
//                closeReadCard();
//            }
            startReadCard();
        }
    }

    public void finishPayment() {
        isFinishedPayment = true;
        cbProcessCard = null;
        closeReadCard();
    }

    protected void cancelAsyncPayment() {
        if (asyncStartPayment != null) {
            asyncStartPayment.cancel(true);
            asyncStartPayment = null;
        }
    }

    public void updateAmountCurrentCy() {
        if (dataPrePay != null) {
            getAmountPay();
        }
    }

    private static class AsyncStartPayment extends AsyncTask<Void, Void, Void> {

        WeakReference<LibReaderController> weakReference;

        AsyncStartPayment(LibReaderController helper) {
            this.weakReference = new WeakReference<>(helper);
        }


        @Override
        protected Void doInBackground(Void... voids) {
            Utils.LOGD(TAG, "doInBackground: ====>>");
            if (weakReference != null && !isCancelled() && weakReference.get().context != null) {
                weakReference.get().startReadCard();
            }
            return null;
        }
    }

    void onSuccessSale(JSONObject jRoot, JSONObject jApproval) {
        MyTextUtils textUtils = new MyTextUtils();
        String desc = dataPrePay != null && !TextUtils.isEmpty(dataPrePay.getDesc())? textUtils.convertString(dataPrePay.getDesc()) : "";
        String email = dataPrePay != null ? dataPrePay.getEmail() : "";
//        String sAmount = amount;

        String trId = JsonParser.getDataJson(jRoot, "transactionRequestID");
        String txId = JsonParser.getDataJson(jRoot, "transactionID");
        String amount = JsonParser.getDataJson(jRoot, "amount");
        String pan = JsonParser.getDataJson(jApproval, "pan");
        String label = JsonParser.getDataJson(jApproval, "applicationLabel");
        String name = JsonParser.getDataJson(jApproval, "cardHolderName").trim();
        String authCode = JsonParser.getDataJson(jApproval, "authCode");
        String invoiceNumber = JsonParser.getDataJson(jApproval, "invoiceNumber");

        pan = CardUtils.getMaskedPan(pan);

        Utils.LOGD(TAG, "onSuccessSale: TRANDID=" + trId + " txid=" + txId + " AMOUNT=" + amount + " PAN=" + pan
                + " label=" + label + " name=" + name + " authCode=" + authCode + " invoice=" + invoiceNumber + " isLibHandlerSignature:" + isLibHandlerSignature);
        final DataPay dataPay = new DataPay(String.valueOf(trId), txId, amount, pan, label, name, email, desc, udid, trxType);
        dataPay.setAuthCode(authCode);
        dataPay.setInvoiceNumber(invoiceNumber);
        dataPay.setTypeCard(nameTypeCard);
        dataPay.setTransactionDate(String.valueOf(timePayment > 0 ? timePayment : System.currentTimeMillis()));

        appendLogAction(" name:" + name + " PAN:" + pan + " --label:" + label + " --AMOUNT: " + amount + " --TRANDID: " + txId
                + " isLibHandlerSignature:" + isLibHandlerSignature);
        if (isLibHandlerSignature) {
            startSignature(dataPay);
        }
        else {
            returnSuccessPayment(dataPay);
        }
    }

    public void startSignature(final DataPay dataPay) {
        if (!callInitPresale) {
            initPresale();
        }
        if (dataSaleSuccess == null && !TextUtils.isEmpty(dataPay.getWfId())) {
            dataSaleSuccess = new DataSaleRes();
            dataSaleSuccess.setWfId(dataPay.getWfId());
        }
        currStageProcess = STAGE_SIGNATURE;
        libSignature = new LibSignature(context, dataPay, new LibSignature.ItfResultSignature() {
            @Override
            public void appendLogSignature(String s) {
                appendLogAction(s);
            }

            @Override
            public void onFailureSignature(int typeFail, DataError dataError, int typeVoidFail, boolean requestLogin) {
                checkDismissDialog();
                if (autoCheckWaitSignWhenTimeOut && !hasRunCheckSignature && !isRunMacq) {
                    processCheckWaitSign(dataPay);
                }
                else {
                    returnFailurePayment(dataPay, dataError, typeFail, typeVoidFail, requestLogin);
                }
            }

            @Override
            public void onSuccessSignature() {
//            public void onSuccessSignature(WfDetailRes detailRes) {
                checkDismissDialog();
                returnSuccessPayment(dataPay);
            }

            public void checkDismissDialog() {
                try {
                    Utils.LOGD(TAG, "checkDismissDialog: " + dismissSignature);
                    if (dismissSignature){
                        Utils.LOGD(TAG, "checkDismissDialog: " + (dialogSignature == null ? "null" : dialogSignature.isVisible()));
                        if(dialogSignature!=null && dialogSignature.isVisible()) {
                            dialogSignature.dismiss();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        libSignature.setAutoCheckWaitSignWhenTimeOut(autoCheckWaitSignWhenTimeOut);

        PayUtils payUtils = new PayUtils();
        boolean isSkipSignature = payUtils.checkSkipSignature(context, dataPay.getAmount());
        appendLogAction("signature: isSkipSignature=" + isSkipSignature);
        // note: use only certify napas
        if (Utils.checkTypeBuildIsCertify() && isEnterPinOk) {
            sendConfirmPaymentMacq("");
            return;
        }
        if (isSkipSignature) {
            sendConfirmPaymentMacq(null);
        }
        else {
            Utils.LOGD(TAG, "startSignature: isSaveInstanceState=" + isSaveInstanceState + " autoCheckWaitSignWhenTimeOut:" + autoCheckWaitSignWhenTimeOut);
            if (!ScreenUtils.canShowDialog(context) || isSaveInstanceState) {
                appendLogAction("pending signature: cause state isSaveInstanceState");
                dataPayCache = dataPay;
                Utils.LOGD(TAG, "startSignature: store data cache==>" + dataPayCache.getAmount());
            }
            else {
                showDialogSignature(useNewSignature ?
                        DialogNewSignature.newInstance(dataPay)
                        : DialogSignature.newInstance(dataPay), dataPay);
            }
        }
    }

    public void onSaveInstanceState() {
        isSaveInstanceState = true;
        appendLogAction("-+-onSaveInstanceState-+-");
    }

    public void resetSaveInstanceState(boolean value) {
        appendLogAction("resetSaveInstanceState-->" + value);
        isSaveInstanceState = value;
    }

    public void processSignatureWhenResumeScreen() {
        Utils.LOGD(TAG, "processSignatureWhenResumeScreen: currStage=" + currStageProcess+" dataPayCache="+(dataPayCache==null?"null":"have data"));
        isSaveInstanceState = false;
        if (currStageProcess == STAGE_SIGNATURE && dataPayCache != null) {
            appendLogAction("--continue signature from resume screen--0H0==>>>");
            startSignature(dataPayCache);
        }
    }

    /**----------*----------* reversal transaction *----------*----------*
     *----------*----------*----------*----------*----------*/

    void runVoidFailedTransaction(boolean isTimeOut, String errorParent, int typeError){
        runVoidFailedTransaction(isTimeOut, new DataError(ConstantsPay.ERROR_CODE_DEFAULT, errorParent), typeError);
    }

    private void runVoidFailedTransaction(boolean isTimeOut, DataError errorParent, int typeError){
        runVoidFailedTransaction(typeError, txId, pan, holderName, isTimeOut, errorParent);
    }

    void runVoidFailedTransaction(final int typeError, String txId, String pan, String holderName,
                                  final boolean isTimeOut, final DataError dataError) {
        appendLogRequest("VOID_FAILED_TRANSACTION isOpenAppAgain=" + isOpenAppAgain);
        if (isOpenAppAgain) {
            return;
        }
        LibVoidFail libVoidFail = new LibVoidFail(context, amount, udid, resultReversal -> {
            appendLogRequest("VOID_FAILED_TRANSACTION result: " + (resultReversal ? "ok" : "false"));
            if (currStageProcess != STAGE_PROCESS_END) {
                if (!resultReversal) {
                    dataError.setMsg(getString(R.string.error_reversal_fail));
                }
                returnFailurePayment(null, dataError, typeError,
                        resultReversal ? LibReaderController.STATUS_VOID_FAIL_RUN_SUCCESS : LibReaderController.STATUS_VOID_FAIL_RUN_FAILURE,
                        isTimeOut);
            }
        });
        libVoidFail.setAppendLog(cbSaveLog);
        if (isRunMacq) {
            libVoidFail.runVoidFailTransactionMacq();
        }
        else {

            libVoidFail.runVoidFailedTransaction(isTimeOut, dataError);
        }
    }

    /**----------*----------* return result pay *----------*----------*
     *----------*----------*----------*----------*----------*/
    private void returnSuccessPayment(DataPay dataPay) {
        enableButtonWhenFinish();
        currStageProcess = STAGE_PROCESS_END;
        if (cbResultPay != null) {
            cbResultPay.onSuccessPay(dataPay);
        } else {
            Utils.LOGW(TAG, "onSuccessSale but not listener callback");
        }
        PrefLibTV.getInstance(context).setLastErrorSale(TYPE_ERROR_NONE);
    }

    protected void returnFailurePayment(DataPay dataPay, DataError dataError, int typeFail, int typeVoidFail, boolean requestLogin) {
        appendLogAction("returnFailurePayment: typeF="+typeFail+" typeVoidF="+typeVoidFail
                +" relogin="+requestLogin+" msg:"+(dataError==null?"":dataError.getMsg())
                +" code:"+(dataError==null?"":dataError.getErrorCode())
        );
        enableButtonWhenFinish();
        currStageProcess = STAGE_PROCESS_END;

        if (dataPay == null) {
            dataPay = new DataPay(amount, "", udid);
        }
        else if (TextUtils.isEmpty(dataPay.getUdid())) {
            dataPay.setUdid(udid);
        }
        dataPay.setTypeCard(nameTypeCard);
        if (TextUtils.isEmpty(dataPay.getPan()) && !TextUtils.isEmpty(maskedPan)) {
            dataPay.setPan(maskedPan);
        }
        if (cbResultPay != null) {
            cbResultPay.onFailPay(dataPay, dataError, typeFail, typeVoidFail, requestLogin);
        }
        else if (cbProcessCard != null) {
            cbProcessCard.onErrorReadCard(dataError);
            finishPayment();
        }
        else {
            Utils.LOGW(TAG, "onFailPay -- not listener callback");
        }
        PrefLibTV.getInstance(context).setLastErrorSale(typeFail);
    }

    /**----------*----------* dialog select application *----------*----------*
     *      show dialog select application
     *----------*----------*----------*----------*----------*/
    protected void showDialogSelectApplication(final List<String> appList, @NonNull ItfChoseApplication callback) {
        runOnUiThread(() -> {
            sendActionTracking(DataTracking.ActionTracker.select_application, "Show");
            final Dialog dialog = new Dialog(context);
            dialog.setContentView(R.layout.dsp_emv_app_dialog);
            dialog.setCancelable(false);
            ((TextView) dialog.findViewById(R.id.tv_title)).setText(R.string.please_select_app);

            String[] appNameList = null;
            if (appList != null) {
                Object[] objArr = appList.toArray();

                appNameList = Arrays.copyOf(objArr, objArr.length, String[].class);
            }
            if (appNameList == null){
                appNameList = new String[0];
            }
            if (autoSelectAppNapas) {
                int numAppNapas = 0;
                int indexAppNapas = -1;
                for (int i = 0; i < appNameList.length; ++i) {
                    Utils.LOGD(TAG, "i=" + i + "," + appList.get(i));
                    appNameList[i] = appList.get(i);
                    if (appList.get(i).toUpperCase().contains(defaultAppNameNapasSelected.toUpperCase())) {
                        numAppNapas++;
                        indexAppNapas = i;
                    }
                }
                if (numAppNapas == 1) {
                    sendActionSelectApp(appList, indexAppNapas);
                    callback.onChoseApplication(indexAppNapas);
                    return;
                }
            }

            Log.d(TAG, "showDialogSelectApplication: ->" + Arrays.toString(appNameList) + " autoSelectAppNapas=" + autoSelectAppNapas);
            ListView appListView = dialog.findViewById(R.id.appList);
            appListView.setAdapter(new SelectApplicationAdapter(context, appNameList));
//            appListView.setAdapter(new ArrayAdapter<>(context, android.R.layout.simple_list_item_1, appNameList));
            appListView.setOnItemClickListener((parent, view, position, id) -> {
                dialog.dismiss();
                appendLogAction(">> select App pos=" + position);
                Utils.LOGD(TAG, "select App -- end position = " + position);
                sendActionSelectApp(appList, position);
                callback.onChoseApplication(position);
            });
            dialog.findViewById(R.id.cancelButton).setOnClickListener(v -> {
                dialog.dismiss();
                appendLogAction(">>cancel select App");
                sendActionTracking(DataTracking.ActionTracker.select_application, "Cancel");
                callback.onChoseApplication(-1);
            });
            dialog.show();
        });
    }
    private void sendActionSelectApp(List<String> appList, int position){
        try {
            sendActionTracking(DataTracking.ActionTracker.select_application, "Select app: " + appList.get(position));
        } catch (Exception e) {
            appendLogAction("error tracking select app:" + e.getMessage());
        }
    }


    /**----------*----------* Bank sale *----------*----------*
     *      api + handler MA
     *----------*----------*----------*----------*----------*/

    protected void sendMagstripeSalesBank(final String mPinBlock) {
        initMagstripeSale();
        StringEntity entity = null;
        appendLogRequest(ConstantsPay.PAYMENT_MAGSTRIPE+(TextUtils.isEmpty(mPinBlock)?" -1-":" -2-"));
        Utils.LOGD(TAG, "magstripeSales: ----> call sale");
        if (TextUtils.isEmpty(mPinBlock)) {
            currStageProcess = STAGE_PROCESS_MAGSTRIPE_SALE;
        }
        else {
            currStageProcess = STAGE_PROCESS_MAGSTRIPE_PIN;
        }
        updateViewByStage(UI_STAGE_SENDING_DATA);
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.PAYMENT_MAGSTRIPE);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", udid);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("sessionKey", ssK);
            jo.put("amount", amount);
            addAmountTrxTypeToRequest(jo);

            jo.put("magstripeData", encDataMag);
            jo.put("magstripeTrack1Data", encTrack1);
            jo.put("magstripeTrack2Data", encTrack2);
            jo.put("readerType", getReaderType());//Data key

            jo.put("restrictInternationCard", PrefLibTV.getInstance(context).getRestrictInternationalCard());

            jo.put("KSN", MyTextUtils.removeSpace(ksn));
            jo.put("isFallback", isFallBack);
            jo.put("longitude", PrefLibTV.getInstance(context).getLongtitude());
            jo.put("latitude", PrefLibTV.getInstance(context).getLatitude());
            if (!TextUtils.isEmpty(mPinBlock)) {
                jo.put("encryptedPINBlock", MyTextUtils.removeSpace(mPinBlock));
            }
            addMoreFieldSale(jo);

            Utils.LOGD(TAG, jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        } catch (Exception e1) {
            e1.printStackTrace();
            appendLog(ItfAppendLog.TypeLog.error, "error build PAYMENT_MAGSTRIPE: "+e1.getMessage());
        }

        MposRestClient.getInstance(context).setPaymentTimeout().post(context, ConstantsPay.getUrlServer(context), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    String ssK = PrefLibTV.getInstance(context).getSessionKey();
                    Utils.LOGD(TAG, "new String(arg2):" + (new String(arg2))
                            + " sskey:" + ssK);

                    JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssK));
                    PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));
                    Utils.LOGD("Magstripe bd sales: ", jRoot.toString());

                    if (jRoot.has("error")) {
                        checkErrorIsByCardData(jRoot, encDataMag+"--t1:"+encTrack1+"--t2:"+encTrack2);
                        appendLogRequest(ConstantsPay.PAYMENT_MAGSTRIPE + ": error in server:");
                        // maybe error duplicate UDID
//                        if (testNapas) {
//                            final JSONObject jError = jRoot.getJSONObject("error");
//                            int code = jError.getInt("code");
//                            if (code == 8103) {
//                                showDialogWarningUseChip();
//                                return;
//                            }
//                        }
                        handlerErrorSaleRequest(jRoot);
                    } else {
                        try {
                            if (!TextUtils.isEmpty(mPinBlock)) {
                                JSONObject jo = jRoot.getJSONObject("approval");
                                if (jo.getJSONObject("status").getInt("code") == 100) {
                                    appendLogRequest("response PAYMENT_MAGSTRIPE: pin + approval");
                                    onSuccessSale(jRoot, jo);
                                } else {
                                    JSONObject jStatus = jo.getJSONObject("status");
                                    appendLogRequest("response PAYMENT_MAGSTRIPE: pin + not approval "+jStatus.getInt("code"));
//                                    showDialogError(jStatus.getInt("code"));
                                    handlerErrorSaleRequest(jStatus.getInt("code"));
                                }
                            } else {
                                // check request input pin
                                if (jRoot.has("eZPK")) {
                                    String mEZPK = jRoot.getString("eZPK");

                                    appendLogRequest("response PAYMENT_MAGSTRIPE: eZPK");

                                    mEZPK = MyTextUtils.removeSpace(mEZPK);

                                    handleWorkingKeyFromServer(mEZPK);
                                }

                                if (jRoot.has("approval")) {
                                    JSONObject jo = jRoot.getJSONObject("approval");
                                    if (jo.getJSONObject("status").getInt("code") == 100) {
                                        appendLogRequest("response PAYMENT_MAGSTRIPE: approval + ok");
                                        onSuccessSale(jRoot, jo);
                                    } else {
                                        JSONObject jStatus = jo.getJSONObject("status");
                                        appendLogRequest("response PAYMENT_MAGSTRIPE: approval + false code:"+jStatus.getInt("code"));
                                        handlerErrorSaleRequest(jStatus.getInt("code"));
//                                        showDialogError(jStatus.getInt("code"));
                                    }
                                }
                            }
                        } catch (JSONException e) {
                            appendLogRequest("response PAYMENT_MAGSTRIPE: JSONException ");
                            e.printStackTrace();
                            showDialogError(getString(R.string.error_try_again));
                        }
                    }
                } catch (Exception e) {
                    appendLogRequest("response PAYMENT_MAGSTRIPE: Exception (timeout)");
                    e.printStackTrace();
                    showDialogErrorTimeOutSession(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                Utils.LOGE(TAG,"PAYMENT_MAGSTRIPE Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                appendLogAction("PAYMENT_MAGSTRIPE onFailure>>" + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                handlerTimeoutSale();
            }
        });
    }

    protected void sendEmvSalesBank(String mPrivateTag) {
        if (BuildConfig.DEBUG && testZ3) {
            sendDenialToTerminal();
            return;
        }
        initEmvSale(isContactLess);
        appendLogRequest(ConstantsPay.PAYMENT_EMV);
        currStageProcess = STAGE_PROCESS_EMV_SALE;
        updateViewByStage(UI_STAGE_SENDING_DATA);
        StringEntity entity = null;
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.PAYMENT_EMV);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", udid);
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("sessionKey", ssK);
            jo.put("amount", amount);
            jo.put("isNFC", isContactLess);

            addAmountTrxTypeToRequest(jo);

            jo.put("emvProcessOfflineResult", 1);
            jo.put("terminalVerificationResults", "");
            jo.put("transactionStatusInfo", "");
            jo.put("readerType", getReaderType());//Data key
            jo.put("emvTags", mPrivateTag);

            jo.put("longitude", PrefLibTV.getInstance(context).getLongtitude());
            jo.put("latitude", PrefLibTV.getInstance(context).getLatitude());

            jo.put("emvRestrictInternationCard", PrefLibTV.getInstance(context).getEmvRestrictInternationalCard());

            addMoreFieldSale(jo);

            Utils.LOGD(TAG, "Data=" + jo);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        } catch (Exception e1) {
            e1.printStackTrace();
            appendLog(ItfAppendLog.TypeLog.error, "error build PAYMENT_EMV: "+e1.getMessage());
        }
        Utils.LOGD(TAG, "emvSales: 11--ConstantsPay.getUrlServer(context)=" + ConstantsPay.getUrlServer(context));
        Utils.LOGD(TAG, "emvSales: send ssk="+ssK);

        MposRestClient.getInstance(context).setPaymentTimeout().post(context, ConstantsPay.getUrlServer(context), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                    PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));
                    Utils.LOGD(TAG, "emvSales: recei ssk="+PrefLibTV.getInstance(context).getSessionKey());
                    Utils.LOGD("EMV sales ", jRoot.toString());
                    if (jRoot.has("error")) {
                        appendLogRequest("PAYMENT_EMV--- error from server");
//                        JSONObject jError = jRoot.getJSONObject("error");
//                        int code = jError.getInt("code");
//                        if (BuildConfig.DEBUG && code == 8090) {
////                            sendErrorCodeToPos("Z3", null);
//                             sendDenialToTerminal();
//                        }
//                        else {
                        checkErrorIsByCardData(jRoot, mPrivateTag);
                        handlerErrorSaleRequest(jRoot);
//                        }
                    }
                    else {
                        try {
                            joRootEmvSale = jRoot;
                            joApproval = jRoot.getJSONObject("approval");

                            if (joApproval.getJSONObject("status").getInt("code") == 100) {
                                appendLogRequest("PAYMENT_EMV--- approval");
                                if (isContactLess) {
                                    onSuccessSale(jRoot, joApproval);
                                }
                                else {
                                    if (jRoot.has("emvTags")) {
                                        emvScript = jRoot.getString("emvTags");
                                    } else {
                                        emvScript = "";
                                    }
                                    runScriptEMV(emvScript);
                                }
                            }
                            else {
                                appendLogRequest("PAYMENT_EMV--- fail");
                                JSONObject jStatus = joApproval.getJSONObject("status");
                                int codeError =  jStatus.getInt("code");
                                appendLogRequest(">>fail:" + codeError);
                                handlerErrorSaleRequest(codeError);
                                if (BuildConfig.DEBUG && testAmex) {
                                    if (jRoot.has("emvTags")) {
                                        emvScript = jRoot.getString("emvTags");
                                    } else {
                                        emvScript = "";
                                    }
                                    sendErrorCodeToPos(String.valueOf(codeError), emvScript);
                                }
                            }
                        } catch (JSONException e) {
                            appendLogRequest("response PAYMENT_EMV: JSONException ");
                            e.printStackTrace();
                            showDialogError(getString(R.string.error_try_again));
                        }
                    }
                } catch (Exception e) {
                    appendLogRequest("PAYMENT_EMV Exception(timeout)");
                    e.printStackTrace();
                    showDialogErrorTimeOutSession(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                appendLogRequest("PAYMENT_EMV onFailure>>"+ MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));

                Utils.LOGE(TAG, "EMV sales bp Error: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3)
                        + " urlserver=" + ConstantsPay.getUrlServer(context));

                if (BuildConfig.DEBUG && testAmex) {
                    sendDenialToTerminal();
                }
                else {
                    handlerTimeoutSale();
                }
            }
        });
    }

    protected void sendConfirmEmvTransactionBank(String emvTagData, final JSONObject jRoot, final JSONObject jApproval){
        appendLogRequest(ConstantsPay.CONFIRM_EMV_TRANSACTION);
        currStageProcess = STAGE_PROCESS_EMV_CONFIRM;
        updateViewByStage(UI_STAGE_SENDING_DATA);
        StringEntity entity = null;
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            int trId = jRoot.getInt("transactionRequestID");
            this.pan = jApproval.getString("pan");
            this.holderName = jApproval.getString("cardHolderName");
            this.txId = jRoot.getString("transactionID");

            JSONObject jo = new JSONObject();
            jo.put("serviceName", ConstantsPay.CONFIRM_EMV_TRANSACTION);
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("udid", "0");
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("sessionKey", ssK);
            jo.put("transactionRequestID", trId);
            jo.put("longitude", PrefLibTV.getInstance(context).getLongtitude());
            jo.put("latitude", PrefLibTV.getInstance(context).getLatitude());
            jo.put("emvTags", emvTagData);
            jo.put("readerType", getReaderType());

            jo.put("trxType", trxType);

            Utils.LOGD("Data: ", jo.toString());
            //			entity = new StringEntity(jo.toString());
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        } catch (Exception e1) {
            e1.printStackTrace();
            appendLog(ItfAppendLog.TypeLog.error, "error build CONFIRM_EMV: "+e1.getMessage());
        }

        Utils.LOGD(TAG, "CONFIRM_EMV: 11--ConstantsPay.getUrlServer(context)=" + ConstantsPay.getUrlServer(context));
        Utils.LOGD(TAG, "CONFIRM_EMV: send ssk=" + ssK);


        MposRestClient.getInstance(context).setPaymentTimeout().post(context, ConstantsPay.getUrlServer(context), entity, ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
            @Override
            public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
                try {
                    JSONObject response = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), PrefLibTV.getInstance(context).getSessionKey()));
                    PrefLibTV.getInstance(context).setSessionKey(response.getString("sessionKey"));
                    Utils.LOGD(TAG, "response confirm payment sales: " + response);
                    if (response.has("error")) {
                        appendLogRequest(ConstantsPay.CONFIRM_EMV_TRANSACTION+" error");
                        handlerErrorSaleRequest(response);
                    } else {
                        appendLogRequest(ConstantsPay.CONFIRM_EMV_TRANSACTION+" ok ");
                        processAlertRemoveCard();
                        onSuccessSale(jRoot, jApproval);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    appendLogRequest(ConstantsPay.CONFIRM_EMV_TRANSACTION+" exception");
                    showDialogErrorTimeOutSession(getString(R.string.SERVICE_ERROR_SESSION_TIMEOUT_L2));
                }
            }

            @Override
            public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
                appendLogRequest("CONFIRM_EMV onFailure>>" + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                Utils.LOGE(TAG, "CONFIRM_EMV onFailure: " + MposRestClient.buildMsgErrorOnFail(arg0, arg1, arg2, arg3));
                handlerTimeoutSale();
            }
        });
    }

    protected void sendConfirmEmvSales() {
        runOnUiThread(() -> {
            if (isRunMacq) {
                    updateViewByStage(UI_STAGE_SENDING_DATA);
                    sendMsgConfirmEmvMacq();
            }
            else {
                sendConfirmEmvTransactionBank(mDataEmv,
                        joRootEmvSale,
                        joApproval);
            }
        });
    }

    /**----------*----------* multi acquirer sale *----------*----------*
     *      api + handler MA
     *----------*----------*----------*----------*----------*/

    protected void sendMagstripeSaleMacq(String pinBlock) {
        initMagstripeSale();

        DataSaleSend dataSaleSend = buildDataSaleSendMacq();
        dataSaleSend.setMagstripeData(MyTextUtils.removeSpace(encDataMag));
        dataSaleSend.setMagstripeTrack1Data(MyTextUtils.removeSpace(encTrack1));
        dataSaleSend.setMagstripeTrack2Data(MyTextUtils.removeSpace(encTrack2));
        dataSaleSend.setFallback(isFallBack);

        appendLogAction("2-encT1=" + (TextUtils.isEmpty(encTrack1) ? "empty" : "exit-" + encTrack1.length())
                + " encT2=" + (TextUtils.isEmpty(encTrack2) ? "empty" : "exit-" + encTrack2.length()));

        if (TextUtils.isEmpty(pinBlock)) {
            currStageProcess = STAGE_PROCESS_MAGSTRIPE_SALE;
        }
        else {
            currStageProcess = STAGE_PROCESS_MAGSTRIPE_PIN;

            dataSaleSend.setEncryptedPINBlock(pinBlock);
            dataSaleSend.setForceAcquirer(preSaleConfig.getAcquirerMagstripe());
        }
        updateViewByStage(UI_STAGE_SENDING_DATA);

        sendMsgSaleMacq(dataSaleSend);
    }

    protected void sendEmvSalesMacq() {
        initEmvSale(isContactLess);
        DataSaleSend dataSaleSend = buildDataSaleSendMacq();

        dataSaleSend.setEmvTags(mDataEmv);
        dataSaleSend.setNFC(isContactLess);

        if (Utils.checkTypeBuildIsCertify() && isContactLess) {
            dataSaleSend.setMagstripeTrack1Data(encTrack1);
            dataSaleSend.setMagstripeTrack2Data(encTrack2);
        }

        currStageProcess = STAGE_PROCESS_EMV_SALE;
        updateViewByStage(UI_STAGE_SENDING_DATA);

        sendMsgSaleMacq(dataSaleSend);
    }

    protected DataSaleSend buildDataSaleSendMacq() {
        changeAmountServiceMacq();
        DataSaleSend dataSaleSend = new DataSaleSend(udid, PrefLibTV.getInstance(context).getSerialNumber(), Build.VERSION.RELEASE, amount,
                trxType, PrefLibTV.getInstance(context).getLongtitude(), PrefLibTV.getInstance(context).getLatitude(), ksn, getReaderType(),
                PrefLibTV.getInstance(context).getUserId()
                , PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_MID, String.class)
                , PrefLibTV.getInstance(context).get(PrefLibTV.MPOS_TID, String.class)
        );
        if (dataPrePay != null) {
            if (!TextUtils.isEmpty(dataPrePay.getDesc())) {
                dataSaleSend.setDescription(dataPrePay.getDesc());
            }
            if (!TextUtils.isEmpty(dataPrePay.getEmail())) {
                dataSaleSend.setCustomerEmail(dataPrePay.getEmail());
            }
            if (!TextUtils.isEmpty(dataPrePay.getPhoneNumber())) {
                dataSaleSend.setCustomerPhone(dataPrePay.getPhoneNumber());
            }
        }
        dataSaleSend.setCheckSignature(true);

        if (!trxType.isEmpty() && trxType.equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
            if ((dataPrePay != null) && (!dataPrePay.getOrderId().isEmpty())) {
                dataSaleSend.setDepositRefCode(dataPrePay.getOrderId());
            } else {
                String timeCode = Utils.convertTimestamp(System.currentTimeMillis(), 6).replaceAll("/", "").replaceAll(":", "").replaceAll(" ", "");
                String depositCode = String.format(ConstantsPay.FORMAT_DEPOSIT_CODE, PrefLibTV.getInstance(context).getUserId(), timeCode);
                dataSaleSend.setDepositRefCode(depositCode);
            }

            if (PrefLibTV.getInstance(context).get(PrefLibTV.allowDeposit, String.class).equals(Constants.SVALUE_2)) {
                dataSaleSend.setDepositMultiTransApproved(true);
            }
        }

        return dataSaleSend;
    }

    protected void sendMsgSaleMacq(@NonNull DataSaleSend dataSale) {
        // add promotion to dataSale
        boolean hasPromotion = PrefLibTV.getInstance(context).get(PrefLibTV.onOffPromotion, Boolean.class, false);
        Utils.LOGD(TAG, "sendMsgSaleMacq: hasPromotion=" + hasPromotion);
        if (hasPromotion) {
            dataSale.setCheckPromotion(1);
            if (arrPromotionsSelected != null && arrPromotionsSelected.size() > 0) {
                dataSale.setArrPromotions(arrPromotionsSelected);
            }
        }
        sendMsgSaleMacqHasPromotion(dataSale);
    }
    protected void sendMsgSaleMacqResetPromotion(@NonNull DataSaleSend dataSale) {
        dataSale.setCheckPromotion(0);
        dataSale.setArrPromotions(null);
        sendMsgSaleMacqHasPromotion(dataSale);
    }
    protected void sendMsgSaleMacqHasPromotion(@NonNull DataSaleSend dataSale) {

        disableButtonWhenSales();
        sendActionTracking(DataTracking.ActionTracker.sale, dataSale.getAmount());
        dataSale.trimFields();

        Utils.LOGD(TAG, "sendMsgSale: ---->>" + ApiMultiAcquirerInterface.URL_NEW_PAYMENT);

        dataSaleSuccess = null;

        if (PrefLibTV.getInstance(context).get(PrefLibTV.allowSaleInternational, Boolean.class)) {
            MposRestClient.getInstance(context).addHeader("allow_sale_international", "true");
            MposRestClient.getInstance(context).addHeader("merchant_id", PrefLibTV.getInstance(context).getMerchantsId());
            appendLogAction("send msg sale MA: allow international");
        } else {
            appendLogAction("send msg sale MA");
        }

        MposRestClient.getInstance(context).setPaymentMATimeout().postMacq(context, ApiMultiAcquirerInterface.URL_NEW_PAYMENT,
                dataSale, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "NewPayment onFailure() called with: statusCode = [" + statusCode + "], " +
                        "headers = [" + Arrays.toString(headers) + "], " +
                        "rawJsonData = [" + rawJsonData + "], " +
                        "throwable = [" + throwable + "], " +
                        "\nisOpenAppAgain=" + isOpenAppAgain);

                String errorCodeFromHeader = MacqUtil.getInstance().parseErrorCodeMacqFromHeader(headers);
                if ("403_ONLY_VN_ACCEPTED".equalsIgnoreCase(errorCodeFromHeader)) {
                    appendLogRequest("onFail  sale MA: 403_ONLY_VN_ACCEPTED");
                    DataError dataError = new DataError(ConstantsPay.ERROR_CODE_403, getString(R.string.error_403_ONLY_VN_ACCEPTED));
                    showDialogError(dataError);
                }
                else {
                    String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);

                    appendLogRequest("onFail  sale MA: " + clearData
                            +" statusCode = [" + statusCode + "],"
                            + "headers = [" + Arrays.toString(headers) + "], "
                            + "throwable = [" + throwable + "], ");
                    handleFailApiMA(true, statusCode, clearData);
                }
                removeHeader();
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "NewPayment onSuccess() called with: statusCode = [" + statusCode + "], " +
                        "headers = [" + Arrays.toString(headers) + "], " +
                        "rawJsonResponse = [" + rawJsonResponse + "], " +
                        "\nisOpenAppAgain=" + isOpenAppAgain);

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                Utils.LOGD(TAG, "NewPayment onSuccess: " + clearData);
                DataSaleRes dataSaleRes = MyGson.parseJson(clearData, DataSaleRes.class);
                if ("HAS_PROMO".equals(dataSaleRes.getSaleResCode())) {
                    if (dataSaleRes.getArrPromotion() != null && dataSaleRes.getArrPromotion().size()>0) {
                        appendLogAction("hasPromotion: size=" + dataSaleRes.getArrPromotion().size());
                        showPopupPromotion(dataSaleRes.getArrPromotion(), dataSale);
                    } else {
                        String msgPromotion = "Mã khuyến mãi không hợp lệ, bạn có muốn thanh toán bình thường hay không?";
                        MyDialogShow.showDialogInfo(context, msgPromotion, false, "Thanh toán thường", "Quay lại",
                                v -> {
                                    // accept
                                    sendMsgSaleMacqResetPromotion(dataSale);
                                }, v -> {
                                    // skip
                                    showDialogError(ERROR_CODE_MA_DEFAULT);
                                });
                    }
                }
                else if ("00".equals(dataSaleRes.getSaleResCode())) {
                    dataSaleSuccess = dataSaleRes;
                    appendLogAction("skip Signature=" + dataSaleRes.isFlagNoSignature());
                    if (dataSaleRes.isFlagNoSignature() && (nameTypeCard.equals(ConstantsPay.CARD_MAGSTRIPE) || dataSale.isNFC())) {
                        onSuccessSaleMacq(dataSaleSuccess);
                    }
                    else {
                        handleSuccessSaleMacq(dataSale, dataSaleRes);
                    }
                }
                else {
                    try {
                        int saleResponseCode = Integer.parseInt(dataSaleRes.getSaleResCode());
                        checkErrorIsByCardData(saleResponseCode, dataSale.getEmvTags());
                        checkNeedResetInjectKey(saleResponseCode);
                        showDialogError(saleResponseCode);
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                        showDialogError(ERROR_CODE_MA_DEFAULT);
                    }
                }
                removeHeader();

            }

                    public void removeHeader() {
                        MposRestClient.getInstance(context).removeHeader("allow_sale_international");
                        MposRestClient.getInstance(context).removeHeader("merchant_id");
                    }
        });
    }

    protected void sendMsgSaleEmart(DataSaleSend dataSale) {
        Utils.LOGD(TAG, "sendMsgSale emart: ---->>");
        dataSale.trimFields();
        dataSaleSuccess = null;
        appendLogAction("send msg sale Emart");
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(MyGson.getGson().toJson(dataSale));

        MposRestClient.getInstance(context).setPaymentMATimeout().post(context, ApiMultiAcquirerInterface.URL_NEW_PAYMENT_EMART, entity, ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "Payment GiftCard onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);
                appendLogRequest("onFail  sale Emart: " + clearData);
                Utils.LOGD(TAG, "Payment GiftCard: " + clearData);
                handleFailEmart(statusCode, clearData);
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "Payment GiftCard called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");
                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                Utils.LOGD(TAG, "Payment GiftCard: " + clearData);
                appendLogAction("Payment GiftCard: " + clearData);
                DataPayEmart dataPayEmart = MyGson.parseJson(clearData, DataPayEmart.class);

                if (dataPayEmart.getResult().getStatus().equals("SUCCESS")) {
                    DataPay dataPay = new DataPay(dataPayEmart.getResult().getAmount(), "", "");
                    dataPay.setPan(dataPayEmart.getUserCard().getPan());
                    dataPay.setLabel(dataPayEmart.getUserCard().getApplicationLabel());
                    dataPay.setTransactionDate(dataPayEmart.getUserCard().getTransactionDate());
                    dataPay.setUdid(dataPayEmart.getOrderCode());
                    dataPay.setAuthCode(dataPayEmart.getAuthCode());

                    Utils.LOGD(TAG, "dataPay= " + MyGson.getGson().toJson(dataPay));
                    returnSuccessPayment(dataPay);
                } else {
                    handleFailApiEmart(clearData);
                }
            }
        });
    }

    private void handleFailEmart(int statusCode, String clearData) {
        DataError dataError = MyGson.parseJson(clearData, DataError.class);
        dataError.setHttpCode(statusCode);

        if (!TextUtils.isEmpty(dataError.getMsg())) {
            showDialogError(dataError.getMsg());
        }
        else {
            showDialogError(context.getString(R.string.transaction_error_default));
        }
    }

    protected void sendMsgConfirmEmvMacq(){
        DataTcSend dataConfirmPaymentSend = new DataTcSend(dataSaleSuccess.getWfId());
        Utils.LOGD(TAG, "sendMsgConfirmEmv: ---->>");
        appendLogAction("send confirm EMV MA");
        currStageProcess = STAGE_PROCESS_EMV_CONFIRM;

        MposRestClient.getInstance(context).postMacq(context, ApiMultiAcquirerInterface.URL_CONFIRM_EMV,
                dataConfirmPaymentSend, new MyTextHttpResponseHandler(context) {

            @Override
            public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                Utils.LOGD(TAG, "CONFIRM_EMV onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);

                Utils.LOGD(TAG, "CONFIRM_EMV onFailure: " + clearData);
                appendLogRequest("onFail  confirmEmv MA: " + clearData+" isServerNoRequireSignature= " + dataSaleSuccess.isFlagNoSignature());

                if (dataSaleSuccess.isFlagNoSignature()) {
                    onSuccessSaleMacq(dataSaleSuccess);
                }
                else {
                    handleFailApiMA(statusCode, clearData);
                }
            }

            @Override
            public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                Utils.LOGD(TAG, "CONFIRM_EMV onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                Utils.LOGD(TAG, "CONFIRM_EMV onSuccess: " + clearData);

                onSuccessSaleMacq(dataSaleSuccess);
            }
        });
    }

    protected void onSuccessSaleMacq(DataSaleRes dataSaleSuccess) {
        MyTextUtils textUtils = new MyTextUtils();
        String desc = dataPrePay != null && !TextUtils.isEmpty(dataPrePay.getDesc())? textUtils.convertString(dataPrePay.getDesc()) : "";
        String email = dataPrePay != null ? dataPrePay.getEmail() : "";
        String sAmount = String.valueOf(amount);

        String trId = "";
        String txId = dataSaleSuccess.getTxid();
        String amount = dataSaleSuccess.getAmount();
        String pan = dataSaleSuccess.getPan();
        String label = dataSaleSuccess.getIssuerCode();
        String name = dataSaleSuccess.getCardHolderName();
        String authCode = dataSaleSuccess.getAuthCode();
        String invoiceNumber = dataSaleSuccess.getInvoiceNo();

        pan = CardUtils.getMaskedPan(pan);

        Utils.LOGD(TAG, "onSuccessSale: trId=" + trId + " txid=" + txId + " amount=" + amount + " pan=" + pan
                + " label=" + label + " name=" + name
                + " email=" + email + " desc=" + desc
                + " authCode=" + authCode + " invoice=" + invoiceNumber + " isLibHandlerSignature:" + isLibHandlerSignature);
        final DataPay dataPay = new DataPay(trId, txId, sAmount, pan, label, name, email, desc, udid, trxType);
//        WfDetailRes dataDetailRes = new WfDetailRes();
//        if (PrefLibTV.getInstance(context).getMerEmart()) {
//            dataDetailRes.setRrn(dataSaleSuccess.getRrn());
//        }
//        if (trxType.equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
//            dataDetailRes.setDepositRefCode(dataSaleSuccess.getDepositRefCode());
//            dataDetailRes.setAmountDeposit(dataSaleSuccess.getAmountDeposit());
//        }
//        dataPay.setWfDetailRes(dataDetailRes);

        String stringDataSaleRes = MyGson.getGson().toJson(dataSaleSuccess);
        WfDetailRes wfDetailRes = MyGson.parseJson(stringDataSaleRes, WfDetailRes.class);
        if (TextUtils.isEmpty(wfDetailRes.getDescription()) && !TextUtils.isEmpty(desc)) {
            wfDetailRes.setDescription(desc);
        }
        dataPay.setWfDetailRes(wfDetailRes);

        dataPay.setAuthCode(authCode);
        dataPay.setInvoiceNumber(invoiceNumber);
        dataPay.setTypeCard(nameTypeCard);
        dataPay.setWfId(dataSaleSuccess.getWfId());
        dataPay.setTransactionDate(String.valueOf(System.currentTimeMillis()));
        if ((arrPromotionsSelected != null) && !arrPromotionsSelected.isEmpty()){
            appendLogAction("Save log promotion selected: " + arrPromotionsSelected);
            dataPay.setLstPromotionInfo(arrPromotionsSelected);
        }
        appendLogAction(" name:" + name + " mp:" + pan + " --label:" + label + " --amount: " + amount + " --transid: " + txId +
                " wfid=" + dataSaleSuccess.getWfId()
                + " isLibHandlerSignature:" + isLibHandlerSignature);

        if (itfHandleBeforeSignature != null) {
            itfHandleBeforeSignature.onHandleBeforeSignature(dataPay);
        }

        if (dataSaleSuccess.isFlagNoSignature()) {
            showToast(getString(R.string.warning_norequired_signature));
            returnSuccessPayment(dataPay);
        }
        else if (isLibHandlerSignature) {
            startSignature(dataPay);
        }
        else {
            returnSuccessPayment(dataPay);
        }
    }

    void handleFailApiMA(int httpCode, String clearData) {
        handleFailApiMA(false, httpCode, clearData);
    }

    void handleFailApiMA(boolean isSale, int httpCode, String clearData) {
        DataError dataError = MyGson.parseJson(clearData, DataError.class);
        dataError.setHttpCode(httpCode);

        if (dataError.getErrorCode() == 0 && !TextUtils.isEmpty(dataError.getMsg())) {
            dataError.setErrorCode(parseErrorCodeFromFailMacq(dataError.getMsg()));
        }

        checkNeedResetInjectKey(dataError.getErrorCode(), dataError.getMsg());

        if (!TextUtils.isEmpty(dataError.getMsg())) {
            if (dataError.getErrorCode() == 0 && !Utils.checkTypeBuildIsCertify()) {
                dataError.setErrorCode(httpCode);
            }
            if (testNapas && dataError.getErrorCode() == 8103 && itfCertifyMacq!=null) {
                itfCertifyMacq.showDialogUseChip();
            }
            else {
                showDialogError(dataError);
            }
            if (nameTypeCard.equals(ConstantsPay.CARD_EMV)) {
                if (isSale && testNapas && dataError.getErrorCode() > 0 && Utils.checkTypeBuildIsCertify() && itfCertifyMacq != null) {
                    MetaDataMa metaDataMa = parseMetaDataMacq(dataError.getDetails());
                    Utils.LOGD(TAG, "handleFailApiMA: sendErrorCodeToPosMacq: code=" + dataError.getErrorCode() +
                            " sript: " + (metaDataMa == null ? "" : metaDataMa.getFailScriptField()));
                    itfCertifyMacq.sendErrorCodeToPosMacq(dataError.getErrorCode(),
                            metaDataMa==null?"":metaDataMa.getFailScriptField());
                }
                else if (isSale && testNapas && httpCode == 500 && itfCertifyMacq != null) {
                    Utils.LOGD(TAG, "handleFailApiMA: sendDenialMacq");
                    itfCertifyMacq.sendDenialMacq();
                }
            }
        }
        else {
            if (Utils.checkTypeBuildIsCertify() && isSale && testAmex && nameTypeCard.equals(ConstantsPay.CARD_EMV)) {
                sendDenialToTerminal();
            }
            else {
                if (isRunMacq && PrefLibTV.getInstance(context).getSkipSignature()) {
                    processCheckTransMacqSuccess();
                }
                else {
                    handlerTimeoutSale();
                }
            }
        }
    }

    void handleFailApiEmart(String clearData) {
        DataPayEmart dataPayEmart = MyGson.parseJson(clearData, DataPayEmart.class);
        String errMsg = "";
        if (!TextUtils.isEmpty(dataPayEmart.getResult().getStatus()) && dataPayEmart.getResult().getStatus().equals(INVALID_CARD_NUMBER)) {
            errMsg = context.getString(R.string.ERR_EMART_CODE_0003);
        } else if (!TextUtils.isEmpty(dataPayEmart.getResult().getStatus()) && dataPayEmart.getResult().getStatus().equals(INSUFFICIENT_BALANCE)) {
            errMsg = context.getString(R.string.ERR_EMART_CODE_0006);
        } else {
            errMsg = context.getString(R.string.ERR_EMART_CODE_DEFAULT, dataPayEmart.getResult().getStatus());
        }
        showDialogError(errMsg);
    }

    public static MetaDataMa parseMetaDataMacq(List<JsonObject> details) {
        MetaDataMa metaData = null;
        Utils.LOGD(TAG, "parseMetaDataMacq: details=");
        if (details != null && details.size() > 0) {
            for (JsonObject jItem : details) {
                if (jItem.has("metadata")) {
                    metaData = MyGson.parseJson(jItem.get("metadata").toString(), MetaDataMa.class);
                }
            }
        }
        return metaData;
    }

    public int parseErrorCodeFromFailMacq(String msg) {
        int code = 0;
        try {
            if (!TextUtils.isEmpty(msg) && msg.indexOf("-") > 0) {
                String scode = MyTextUtils.removeSpace(msg.substring(0, msg.indexOf("-")));
                if (!TextUtils.isEmpty(scode) && TextUtils.isDigitsOnly(scode)) {
                    code = Integer.parseInt(scode);
                }
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return code;
    }

    void sendConfirmPaymentMacq(String base64Signature) {
        this.base64Signature = base64Signature;
        if (isRunMacq) {
            DataConfirmPaymentSend dataConfirmPaymentSend = new DataConfirmPaymentSend(dataSaleSuccess.getWfId(), base64Signature);
            libSignature.sendMsgConfirmPaymentMA(dataConfirmPaymentSend);
        }
        else {
            libSignature.confirmPayment(base64Signature, 1);
        }
    }

    /**
     * ----------*----------* Promotion *----------*----------*
     * ----------*----------*----------*----------*----------
     */
    protected boolean reSaleAgain = false;
    ArrayList<PromotionInfo> arrPromotionsReceived;
    ArrayList<PromotionInfo> arrPromotionsSelected;
    BottomSheetPromotion dialogPromotion;

    private void showPopupPromotion(ArrayList<PromotionInfo> array, DataSaleSend dataSale) {
        updateViewByStage(UI_STATE_SELECT_PROMOTION);
        currStageProcess = UI_STATE_SELECT_PROMOTION;

        this.arrPromotionsReceived = array;
        dialogPromotion = BottomSheetPromotion.newInstance(array, lAmount);
        dialogPromotion.setCallback(new BottomSheetPromotion.ItfPromotion() {
            @Override
            public void onCancelPromotion() {
                MyDialogShow.showDialogWarning(context, getString(R.string.warning_cancel_transaction),
                        getString(R.string.BTN_CANCEL_TRANS), getString(R.string.BTN_CLOSE),
                        new MyOnClickListenerView(v -> {
                            dialogPromotion.dismiss();
                            showDialogError(getString(R.string.transaction_cancel));
                        }), new MyOnClickListenerView(v -> {

                        }), false);
            }

            @Override
            public void onPayWithPromotion(ArrayList<PromotionInfo> arrSelected) {
//                hasActionPromotion = true;
                showLogArrPromotion(arrSelected);
                handleSelectPromotionForPay(arrSelected, dataSale);
            }

            @Override
            public void onClickDetailPromotion(PromotionInfo promotionInfo) {
//                hasActionPromotion = true;
                showPopupDetailsPromotion(promotionInfo, dataSale);
            }
        });
        dialogPromotion.setCancelable(false);
        dialogPromotion.show(getSupportFragmentManager(), dialogPromotion.getTag());
    }

    private void handleSelectPromotionForPay(ArrayList<PromotionInfo> arrSelectedPromotions, DataSaleSend dataSale) {
        if (arrSelectedPromotions == null || arrSelectedPromotions.size() == 0) {
            sendMsgSaleMacqResetPromotion(dataSale);
        }
        else {
            long newAmount = lAmount;
            for (PromotionInfo promotionInfo : arrSelectedPromotions) {
                try {
                    appendLogAction("promotion: " + promotionInfo.getPromotionCode() + " -" + promotionInfo.getAmountDiscount());
                    newAmount = newAmount - Long.parseLong(promotionInfo.getAmountDiscount());
                } catch (NumberFormatException e) {
                    appendLogAction("error parse amount promotion: " + e.getMessage());
                }
            }
            appendLogAction("amount after promotion: " + newAmount);
            processReSaleWithPromotion(arrSelectedPromotions, String.valueOf(newAmount));
        }
    }

    private void showLogArrPromotion(ArrayList<PromotionInfo> arrayList) {
        if (arrayList != null) {
            for (PromotionInfo promotionInfo : arrayList) {
                Utils.LOGD(TAG, "promotion: " + promotionInfo.getPromotionCode() + " checked=" + promotionInfo.isChecked());
            }
        }
    }


//    boolean isUsePromotion = false;
    private void showPopupDetailsPromotion(PromotionInfo promotionInfo, DataSaleSend dataSale) {
//        isUsePromotion = false;
        BottomSheetPromotionFurther dialog = BottomSheetPromotionFurther.newInstance(promotionInfo);
        dialog.setCallback(promotionDetails -> {
//            isUsePromotion = true;
            for (PromotionInfo promotionItem : arrPromotionsReceived){
                if (promotionItem.getPromotionCode().equals(promotionDetails.getPromotionCode())) {
                    promotionItem.setChecked(promotionDetails.isChecked());
                    if (dialogPromotion != null) {
                        dialogPromotion.swapCheckedItem(promotionDetails);
                    }
                    break;
                }
            }
        });
        dialog.setCancelable(false);
//        dialog.getLifecycle().addObserver(new LifecycleEventObserver() {
//            @Override
//            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
//                Utils.LOGD(TAG, "detail promotion - onStateChanged: event: " + event.name());
//                if (event == Lifecycle.Event.ON_DESTROY) {
//                    if (isUsePromotion && !promotionInfo.isChecked()) {
//                        // change to use promotion
//                        for (PromotionInfo promotion : arrPromotionsReceived){
//                            if (promotion.getPromotionCode().equals(promotionInfo.getPromotionCode())) {
//                                promotion.setChecked(true);
//                                break;
//                            }
//                        }
//                    }
////                    showPopupPromotion(arrPromotionsReceived, dataSale);
//                }
//            }
//        });
        dialog.show(getSupportFragmentManager(), dialog.getTag());
    }

    private FragmentManager getSupportFragmentManager() {
        AppCompatActivity activity = (AppCompatActivity) context;
        return activity.getSupportFragmentManager();
    }


    /**----------*----------* update view by state *----------*----------*
     *----------*----------*----------*----------*----------*/

    protected void updateViewByStage(int stage) {
        if (cbUpdateUI != null) {
            cbUpdateUI.showViewDspreadByStage(stage);
        }
    }

    protected void showNotifyDetectCard() {
        switch (nameTypeCard) {
            case ConstantsPay.CARD_EMV:
                showNotifyPayment(getString(R.string.ntf_detect_chip_card));
                break;
            case ConstantsPay.CARD_NFC:
                showNotifyPayment(getString(R.string.ntf_detect_nfc_card));
                break;
        }
    }
    protected void showNotifyPayment(String message) {
        Utils.LOGD(TAG, "showNotifyPayment: message=" + message);
        if (cbUpdateUI != null && !TextUtils.isEmpty(message)) {
            runOnUiThread(() -> cbUpdateUI.showNotify(new DataNotifyPay("1",message)));
        }
    }

    /**----------*----------* handler error *----------*----------*
     *----------*----------*----------*----------*----------*/
    void handlerErrorSaleRequest(JSONObject jRoot) {
        try {
            DataError dataError = new DataError();
            final JSONObject jo = jRoot.getJSONObject("error");
            int code = jo.getInt("code");

            String msg = (showErrorCodeInMsg?getString(R.string.error) + " " + code+": " : "")
                    + LibError.getErrorMsg(jo.getInt("code"), context);

            appendLogRequest(">>error in server: code="+code+" -->"+msg);

            dataError.setErrorCode(code);
            dataError.setMsg(msg);

            handlerErrorSaleRequest(code, dataError);
        } catch (JSONException e) {
            e.printStackTrace();
            appendLogRequest(">>error parse JsonError from server: "+e.getMessage());
            showDialogError(getString(R.string.error_try_again));
        }
    }

    /**
     * handler error/status from sale request
     * @param code: error code
     */
    void handlerErrorSaleRequest(int code) {
        handlerErrorSaleRequest(code, null);
    }

    void handlerErrorSaleRequest(int code, DataError dataError) {
        appendLogAction("--error sale: " + code);
        if (dataError == null) {
            dataError = new DataError(code, LibError.getErrorMsg(code, context));
        }
        if (code == ConstantsPay.CODE_REQUEST_TIMEOUT) {
            showDialogErrorTimeOutSession(dataError);
        } else {
            int typeDevice = PrefLibTV.getInstance(context).getFlagDevices();
            if (enableInjectKey && typeDevice == ConstantsPay.DEVICE_DSPREAD
                    && (code == 8102 || code == 10007 || code == 10001)) {
                processInjectKey(typeDevice, dataError);
            } else if ((typeDevice == ConstantsPay.DEVICE_DSPREAD || typeDevice == ConstantsPay.DEVICE_P20L || typeDevice == ConstantsPay.DEVICE_KOZEN)
                    && (code == 96 || code == 10007 || code == 10001)) {
                checkNeedResetInjectKey(code);
                if (code == 96 && !isRunMacq) {
                    clearTidEzpk(dataError);
                } else {
                    showDialogError(dataError);
                }
            } else {
                showDialogError(dataError);
            }
        }
    }

    private void processInjectKey(int typeDevice, DataError dataError) {
        appendLogAction("process inject key");
        String serialNumber = PrefLibTV.getInstance(context).getSerialNumber();
        int typeServer = PrefLibTV.getInstance(context).getFlagServer();
        LibInjectKey libInjectKey = new LibInjectKey(context, typeDevice, typeServer, serialNumber);
        libInjectKey.setDspreadControl(this);
//        libInjectKey.setDspreadControl((LibDspreadReader) this);
        libInjectKey.setCbLog((typeLog, s) -> appendLogAction(s));
        libInjectKey.setCallbackShowLoading(this::showLoading);
        libInjectKey.setCallback((typeKey, code, msg) -> {
            appendLogAction("onResultInjectKey() called with: typeKey = [" + typeKey + "], code = [" + code + "], msg = [" + msg + "]");
            if (typeKey == LibInjectKey.TYPE_KEY_END || typeKey == LibInjectKey.TYPE_KEY_NONE) {
                showDialogError(dataError);
            }
        });
        libInjectKey.processInjectKey();
    }

    void handlerTimeoutSale() {
        if (autoCheckWaitSignWhenTimeOut) {
            processCheckWaitSign(null);
        }
//        else if (ConstantsPay.TRX_TYPE_SERVICE.equals(trxType) || needRunVoidFail) {
//            runVoidFailedTransaction(true, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), TYPE_ERROR_SALE_TIMEOUT_SESSION);
//        }
        else {
            showDialogSaleTimeout(TYPE_ERROR_SALE_TIMEOUT_REQUEST);
        }
    }

    private void showDialogSaleTimeout(int typeFail) {
        DataError error = new DataError(ConstantsPay.ERROR_CODE_DEFAULT, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT));
        returnFailurePayment(null, error, typeFail, 0, false);
    }

    protected int CHECK_WAIT_SIGN_SALE      = 1;
    protected int CHECK_WAIT_SIGN_RUN_SCRIPT = 2;
    private int typeCheckWaitSign = CHECK_WAIT_SIGN_SALE;

    protected void processCheckWaitSign(DataPay dataPay) {
        processCheckWaitSign(dataPay, CHECK_WAIT_SIGN_SALE);
    }
    protected void processCheckWaitSign(DataPay dataPay, int type) {
        typeCheckWaitSign = type;
        hasRunCheckSignature = true;
        Utils.LOGD(TAG, "checkWaitSign: --->");
        appendLogAction("checkWaitSign");
        dataPayCache = dataPay;
        LibLoginHandler libLoginHandler = new LibLoginHandler(context, this, dataPrePay.getReaderType().getReaderType());
        libLoginHandler.setCheckWaitSignature(true);
        libLoginHandler.setHandlerTranWaitSignature(true);
        libLoginHandler.fetchMerchantConfig(PrefLibTV.getInstance(context).getUserId(), PrefLibTV.getInstance(context).getSerialNumber(), dataPrePay.getMobilePass());
    }

    protected void processCheckTransMacqSuccess() {
        MposTransactionsMacq mposTranMacq = new MposTransactions(context);
        mposTranMacq.getTransactionStatusMacqByUdid(udid, new MposTransactionsMacq.ItfHandlerActionTrans<WfDetailRes>() {
            @Override
            public void onFailureActionTrans(@NonNull DataError dataError, MposTransactionsMacq.ActionTrans typeAction) {
                Utils.LOGD(TAG, "processCheckTransMacqSuccess: onfail");

                showDialogSaleTimeout(TYPE_ERROR_SALE_TIMEOUT_REQUEST);
            }

            @Override
            public void onSuccessActionTrans(WfDetailRes wfDetailRes, MposTransactionsMacq.ActionTrans typeAction) {
                Utils.LOGD(TAG, "processCheckTransMacqSuccess: success status=" + (wfDetailRes != null ? wfDetailRes.toString() : "obj null"));
                if (wfDetailRes != null &&
                        (MposIntegrationHelper.TRANS_STATUS_APPROVED.equals(wfDetailRes.getStatus())
                                || MposIntegrationHelper.TRANS_STATUS_PENDING_TC.equals(wfDetailRes.getStatus())
                        )
                ) {
                    DataPay dataPay = new DataPay(wfDetailRes);
                    returnSuccessPayment(dataPay);
                }
                else {
                    showDialogSaleTimeout(TYPE_ERROR_SALE_TIMEOUT_REQUEST);
                }
            }
        }, WfDetailRes.class);
    }

    void checkErrorIsByCardData(JSONObject jRoot, String mdata) {
        try {
            final JSONObject jo = jRoot.getJSONObject("error");
            int code = jo.getInt("code");
            checkErrorIsByCardData(code, mdata);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    void checkErrorCodeIsSpecial(DataError dataError) {
        if (dataError != null && dataError.getErrorCode() == 65 && ConstantsPay.CARD_NFC.equals(nameTypeCard)) {
            dataError.setMsg(getString(R.string.ERROR_65_contactless));
        }
    }

    void checkErrorIsByCardData(int code, String mdata) {
        if (code == 10007 || code == 8102  || code == 10001) {
            appendLogAction("error:"+code+" data:"+MyTextUtils.removeSpace(mdata));
        }
    }

    private void checkNeedResetInjectKey(int code) {
        checkNeedResetInjectKey(code, null);
    }

    /**
     * check need reinject key: if reader change to macq and get code error 96 -> clear cache injected -> app will auto inject next sale
     * @param code: code error from macq
     *        msgError: error message from macq
     */
    private void checkNeedResetInjectKey(int code, String msgError) {
        // todo not use certify
        if (code == 96 || code == 10007 || code == 10001 ||
                (!TextUtils.isEmpty(msgError) && (msgError.startsWith("A1 ") || msgError.startsWith("A2 ")))
        ) {
            resetCacheInjectKey();
        }
    }

    protected void resetCacheInjectKey() {
        PrefLibTV.getInstance(context).put(PrefLibTV.readersInjected, "");
        PrefLibTV.getInstance(context).put(PrefLibTV.WORKING_KEY_LAST_OK, "");
        appendLogAction("clear cache injected ->now=" + PrefLibTV.getInstance(context).get(PrefLibTV.readersInjected, String.class));
    }

    protected void enableRetryUpdateEmvConfig() {
        PrefLibTV.getInstance(context).put(PrefLibTV.retryUpdateEmvConfig, true);
    }

    protected String getLastUpdatedEmvConfig() {
        String time = "";
        try {
            long lastTimeUpgradeEmvConfig = PrefLibTV.getInstance(context).get(PrefLibTV.lastTimeUpgradeEMVConfig, Long.class);
            if (lastTimeUpgradeEmvConfig > 0L) {
                time = String.format("\n(LU: %s)", Utils.convertTimestamp(lastTimeUpgradeEmvConfig, 3));
            }
        } catch (Exception e) {
            appendLogAction("error getLastUpgradeEMVConfig: " + e.getMessage());
        }
        return time;
    }

    private void disableButtonWhenSales() {
        if (DevicesUtil.isSP02()) {
            CustomAPI.init(context);

            setNavBar(false);
        }
    }

    private void enableButtonWhenFinish() {
        if (DevicesUtil.isSP02()) {
            try {
                CustomAPI.release();

                setNavBar(true);
            } catch (Exception e) {
                appendLogAction("error: " + e.getMessage());
            }
        }
    }

    void setNavBar(boolean enabled) {
        Utils.LOGD(TAG, "setNavBar: enabled=" + enabled);
        try {
            Intent intent = new Intent();
            intent.setAction("android.intent.action.CUSTOM_ACTION_SET_NVBAR");
            intent.addFlags(Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
            intent.putExtra("homekey_enabled", enabled);
            intent.putExtra("recentkey_enabled", enabled);
            context.sendBroadcast(intent);
        } catch (Exception e) {
            e.printStackTrace();
            appendLogAction("error setNavBar: "+e.getMessage());
        }
    }

    void showDialogError(int code){
        DataError dataError = new DataError(code);
        dataError.setMsg(LibError.getErrorMsg(code, context));
        showDialogError(dataError);
    }

    void showDialogError(String msg){
        msg = TextUtils.isEmpty(msg)?getString(R.string.error_try_again):msg;
        showDialogError(new DataError(ConstantsPay.ERROR_CODE_DEFAULT, msg));
    }

    void showDialogError(DataError error){
        checkErrorCodeIsSpecial(error);
        AppExecutors.getInstance().mainThread().execute(()->returnFailurePayment(null, error, TYPE_ERROR_SALE_FAILURE, 0, false));
//        returnFailurePayment(null, error, TYPE_ERROR_SALE_FAILURE, 0, false);
    }

    void showToast(String msg) {
        try {
            runOnUiThread(() -> Toast.makeText(context, msg, Toast.LENGTH_SHORT).show());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    void showDialogErrorTimeOutSession(String msg) {
        showDialogErrorTimeOutSession(new DataError(msg));
    }

    private void showDialogErrorTimeOutSession(DataError error) {
        returnFailurePayment(null, error, TYPE_ERROR_SALE_TIMEOUT_SESSION, 0, true);
    }

    protected void showDialogWarningRetryPayment(String msg, Runnable runnable) {
        final android.app.AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(getString(R.string.mp_notice));
        builder.setCancelable(false);
        builder.setMessage(msg);
        builder.setPositiveButton(android.R.string.ok, (dialog, whichButton) -> runnable.run());
        builder.setNegativeButton(android.R.string.cancel, (dialog, whichButton) -> {
            DataError dataError = new DataError(ERROR_CODE_FAIL_CHIP_AID, getString(R.string.transaction_cancel));
            returnFailurePayment(null, dataError, TYPE_ERROR_CHIP_AID_FAILURE, 0, false);
        });
        Dialog dialog = builder.create();
        dialog.show();
    }

    /**----------*----------*-----log data-----*----------*----------*
     *
     *----------*----------*----------*----------*----------*/
    void appendLogAction(String log) {
        appendLog(ItfAppendLog.TypeLog.action, log);
    }

    void appendLogRequest(String log) {
        appendLog(ItfAppendLog.TypeLog.request, log);
    }

    private void appendLog(ItfAppendLog.TypeLog action, String log) {
        Utils.LOGD(TAG, "log="+log);
        if (cbSaveLog != null)
            cbSaveLog.appendLog(action, log);
    }

    void sendActionTracking(DataTracking.ActionTracker action, String msg) {
        sendActionTracking(action.toString(), msg);
    }
    void sendActionTracking(String action, String msg) {
        if ((Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd()) && itfHandleActionTracking != null) {
            itfHandleActionTracking.onHandleActionTracker(action, msg);
        }
    }

    /**
     * ----------*----------*----handler signature------*----------*----------*
     * <p>
     * ----------*----------*----------*----------*----------
     */
    private void showDialogSignature(@NonNull DialogBaseSignature dialogSignature, final DataPay dataPay) {
        this.dialogSignature = dialogSignature;
        appendLogAction("show Signature");
        Utils.LOGD(TAG, "showDialogSignature: ");
        AppCompatActivity activity = (AppCompatActivity) context;
        dialogSignature.setCancelable(false);

        dialogSignature.setSdkCustom(sdkCustom);
        dialogSignature.setTITLE_BG_COLOR(TITLE_BG_COLOR);
        dialogSignature.setTITLE_TEXT_COLOR(TITLE_TEXT_COLOR);
        dialogSignature.setCLEAR_TEXT_COLOR(CLEAR_TEXT_COLOR);
        dialogSignature.setINFO_BG_COLOR(INFO_BG_COLOR);
        dialogSignature.setINFO_TEXT_COLOR(INFO_TEXT_COLOR);
        dialogSignature.setCONTINUE_BG_ICON(CONTINUE_BG_ICON);

        dialogSignature.setSIGNATURE_WIDTH(SIGNATURE_WIDTH);
        dialogSignature.setSIGNATURE_HEIGHT(SIGNATURE_HEIGHT);

        dialogSignature.setMoreInfo(moreInfoOfTrans);

        dialogSignature.setCallback(new DialogSignature.ItfHandlerActionSignature() {
            @Override
            public void appendLog(String log) {
                appendLogAction(log);
            }

            @Override
            public void sendSignature(String encodeSignature) {
//            public void sendSignature(String encodeSignature, int numRetry) {
//                libSignature.confirmPayment(encodeSignature, numRetry);
                sendConfirmPaymentMacq(encodeSignature);

                if (dataPay != null && (Constants.isTypeIntegrationMpos() || Constants.isTypeIntegrationMpos3rd())) {
                    // note: show signature in log: because server sometime can not save signature
//                    if (PrefLibTV.getInstance(context).get(PrefLibTV.versionLite, Boolean.class, false)) {
                        dataPay.setSignatureBase64(encodeSignature);
//                    }
                }
            }

//            @Override
//            public void doSomethingHidden() {
//                if (BuildConfig.DEBUG) {
//                    runVoidFailedTransaction(true, getString(R.string.SERVICE_ERROR_REQUEST_TIMEOUT), STAGE_SALE_TIMEOUT_SESSION);
//                }
//            }
        });
        dialogSignature.show(activity.getSupportFragmentManager(), "DialogSignature");
    }

    /**----------*----------*---- handler interface libLogin ------*----------*----------*
     *
     *----------*----------*----------*----------*----------*/
    @Override
    public void showLoading(boolean show) {

    }

    @Override
    public void onFailureLogin(@NonNull DataError dataError, int type) {
        endProcessCheckWaitSign();
    }

    @Override
    public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {
        appendLogAction("reLogin check: have waitSign, currStage=" + currStageProcess + " udidWait=" + dataReversalLogin.paymentIdentify + " udidCheck=" + udid);
        if (dataReversalLogin.paymentIdentify.equals(udid)) {
            if (currStageProcess == STAGE_SIGNATURE) {
                sendConfirmPaymentMacq(base64Signature);
            }
            // timeout when sale
            else {
                DataPay dataPay = new DataPay(dataReversalLogin);
                startSignature(dataPay);
            }
        }
        else {
            endProcessCheckWaitSign();
        }
    }

    @Override
    public void onSuccessLogin() {
        appendLogAction("reLogin check: successLogin, currStage=" + currStageProcess + " DataPay: " + (dataPayCache == null ? "not" : "have"));
        if (currStageProcess == STAGE_SIGNATURE && dataPayCache != null) {
            returnSuccessPayment(dataPayCache);
        }
        else {
            endProcessCheckWaitSign();
        }
    }

    private void endProcessCheckWaitSign() {
        if (typeCheckWaitSign == CHECK_WAIT_SIGN_RUN_SCRIPT) {
            showDialogError(getString(R.string.error_qpos_device_no_response));
        }
        else {
            showDialogSaleTimeout(TYPE_ERROR_CONFIRM_PAYMENT_FAILURE);
        }
    }


    /**----------*----------*----other api------*----------*----------*
     *
     *----------*----------*----------*----------*----------*/

    private void clearTidEzpk(DataError dataError) {
        StringEntity entity = null;
        String ssK = PrefLibTV.getInstance(context).getSessionKey();
        try {
            appendLogRequest("CLEAR_TID_EZPK");
            JSONObject jo = new JSONObject();
            jo.put(Constants.STR_SERVICE_NAME, "CLEAR_TID_EZPK");
            jo.put("readerSerialNo", PrefLibTV.getInstance(context).getSerialNumber());
            jo.put("versionNo", android.os.Build.VERSION.RELEASE);
            jo.put("platform", ConstantsPay.PLATFORM);
            jo.put("userID", PrefLibTV.getInstance(context).getUserId());
            jo.put("udid", "0");
            jo.put("sessionKey", ssK);
            Utils.LOGD(TAG, "Data: "+ jo);
            entity = new StringEntity(EncodeDecode.doAESEncrypt(jo.toString(), ssK));
        }
        catch (Exception e) {
            Utils.LOGE(TAG, "CLEAR_TID_EZPK Exception: ", e);
        }

        MposRestClient.getInstance(context).post(context, ConstantsPay.getUrlServer(context), entity,
                ConstantsPay.CONTENT_TYPE, new AsyncHttpResponseHandler() {
                    @Override
                    public void onStart() {
                        showLoading(true);
                        super.onStart();
                    }
                    @Override
                    public void onFailure(int arg0, Header[] arg1, byte[] arg2, Throwable arg3) {
//                        hideLoading();
                        appendLogRequest("CLEAR_TID_EZPK onFailure");
                        Utils.LOGE(TAG, "CLEAR_TID_EZPK Error: ", arg3);
                        showDialogError(dataError);
                    }
                    @Override
                    public void onSuccess(int arg0, Header[] arg1, byte[] arg2) {
//                        hideLoading();
                        try {
                            JSONObject jRoot = new JSONObject(EncodeDecode.doAESDecrypt(new String(arg2), ssK));
                            PrefLibTV.getInstance(context).setSessionKey(jRoot.getString("sessionKey"));

                            appendLogRequest("CLEAR_TID_EZPK success");
                        } catch (Exception e) {
                            appendLogRequest("CLEAR_TID_EZPK exception:" + e.getMessage());
                            Utils.LOGE(TAG, "Exception", e);
                        }
                        showDialogError(dataError);
                    }

                });
    }

    /**
     * ----------*----------*----callback------*----------*----------*
     * ----------*----------*----------*----------*----------
     */
    protected void callbackCardData() {
        if (cbProcessCard != null) {
            DataProcessCard dataProcessCard = new DataProcessCard(typeProcessCard, encDataMag, encTrack1, encTrack2, ksn, null);
            dataProcessCard.setNameTypeCard(nameTypeCard);
            dataProcessCard.setmDataEmv(mDataEmv);
            dataProcessCard.setReaderType(getReaderType());
            runOnUiThread(() -> {
                cbProcessCard.onSuccessReadCard(dataProcessCard);
                finishPayment();
            });
        }
    }


    //--------------- interface -----------------
    public interface ItfResultPay {
        void onSuccessPay(DataPay dataPay);

        void onFailPay(DataPay dataPay, DataError error, int typeFail, int typeVoidFail, boolean requestLogin);

        void onFinishWithTypeError(int type);   // use for AR + PR01
    }

    public interface ItfCheckMaskPan {
        boolean checkMaskPanCanPay(String maskPan);
    }

    public interface ItfChoseApplication{
        void onChoseApplication(int position);
    }

    protected interface ItfCertifyMacq{
        void showDialogUseChip();

        void sendErrorCodeToPosMacq(int codeError, String emvScript);

        void sendDenialMacq();
    }

    public interface ItfHandleBeforeSignature{

        void onHandleBeforeSignature(DataPay dataPay);
    }

    public interface ItfHandleActionTracking{
        void onHandleActionTracker(String action, String msg);
    }

    public interface ItfHandlePromotion{
//        void onHandlePromotion(ArrayList<PromotionInfo> arrSelectedPromotions, long newAmount);
        void onHandlePromotion(long newAmount);
    }

//    public interface ItfHandleMcp{
//        void onHandleMultiCurrency(ArrayList<McpInfo> arrSelectedMcps, long newAmount);
//    }
}
