package com.mpos.sdk.view;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.mpos.sdk.R;
import com.mpos.sdk.core.modelma.McpInfo;
import com.mpos.sdk.core.modelma.PromotionInfo;
import com.mpos.sdk.databinding.BtsMultiCurrentcyBinding;
import com.mpos.sdk.databinding.BtsPromotionBinding;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.Utils;
import com.nostra13.universalimageloader.core.ImageLoader;
import com.nostra13.universalimageloader.core.ImageLoaderConfiguration;

import java.util.ArrayList;

/**
 * Create on 9/6/25
 */
public class BottomSheetMultiCurrentcy extends BaseBottomSheet {

    private static final String TAG = "BottomSheetMultiCurrentcy";
    ArrayList<McpInfo> arrMcpList;
    MultiCurrentCyAdapter adapter;
    ItfCallbackMcp itfCallbackMcp;
    BtsMultiCurrentcyBinding binding;
    long amountOriginal;
    long amountPay;


    public BottomSheetMultiCurrentcy() {
    }

    @Override
    public int getTheme() {
        return R.style.AppBottomSheetDialogTheme;
    }

    public static BottomSheetMultiCurrentcy newInstance(ArrayList<McpInfo> arrPromotions, long amountOriginal) {

        Bundle args = new Bundle();
        args.putParcelableArrayList("ArrMcpInfo", arrPromotions);
        args.putLong("amountOriginal", amountOriginal);
        BottomSheetMultiCurrentcy fragment = new BottomSheetMultiCurrentcy();
        fragment.setArguments(args);
        return fragment;
    }

    public void setCallback(@NonNull ItfCallbackMcp itfPromotion) {
        this.itfCallbackMcp = itfPromotion;
    }


    @NonNull @Override public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        if (DevicesUtil.isSP02P12() || DevicesUtil.isSP02N4()) {
            dialog.setOnShowListener(dialogInterface -> {
                BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
                setupFullHeight(bottomSheetDialog);
            });
        }
        return  dialog;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = BtsMultiCurrentcyBinding.inflate(getLayoutInflater(), container, false);
        intiView();

        Bundle bundle = getArguments();
        if (bundle != null) {
            arrMcpList = bundle.getParcelableArrayList("ArrMcpInfo");
            amountOriginal = bundle.getLong("amountOriginal");
            if (arrMcpList != null) {
                initData();
            }
        }
        return binding.getRoot();
    }

    private void intiView() {
        binding.tvCancelPay.setOnClickListener(v -> callbackCancel());
        binding.imvClose.setOnClickListener(v -> callbackCancel());
        binding.btnPay.setOnClickListener(v -> {
            callbackPromotionSelected();
        });
    }
    private void initData() {
        amountPay = amountOriginal;
        ImageLoader.getInstance().init(new ImageLoaderConfiguration.Builder(getContext()).build());
        Utils.LOGD(TAG, "initData: size mcp=" + arrMcpList.size());
        adapter = new MultiCurrentCyAdapter(getContext(), arrMcpList, new MultiCurrentCyAdapter.IClickItemChild() {
            @Override
            public void clickItemChild(McpInfo item) {
                Utils.LOGD(TAG, "clickItemChild: ---click item url:"+item.getCurrencyCode());
//                callbackViewDetailPromotion(item);
            }

            @Override
            public void onCheckedChange(McpInfo item) {
                Utils.LOGD(TAG, "onCheckedChange: " + item.getAmount() );
                if (amountPay <= 0) {
                    amountPay = amountOriginal;
                    calculatorAmount();
                }
                else {
//                    updateAmount(item);
                }
            }
        });
        binding.listView.setAdapter(adapter);
//        binding.listView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
        binding.listView.setLayoutManager(new GridLayoutManager(getContext(), 2, LinearLayoutManager.VERTICAL, false));


        // check promotion is default checked
        calculatorAmount();
        showAmountPay();
    }

    private void calculatorAmount() {
        for (McpInfo info : arrMcpList){
            updateAmount(info);
        }
    }

    private void showAmountPay() {
        binding.tvTotalAmount.setText(String.format("%s%s", Utils.zenMoney(amountPay), ConstantsPay.CURRENCY_SHORT));
    }


    private synchronized void updateAmount(McpInfo details) {
        if (details != null) {
            binding.btnPay.setEnabled(true);
            showAmountPay();
        }
    }

    public void swapCheckedItem(McpInfo promotionDetails) {
        for (McpInfo promotionItem : arrMcpList) {
            if (promotionItem.getFee().equals(promotionDetails.getFee())) {
                adapter.notifyDataSetChanged();
//                adapter.notifyItemRangeChanged(0, arrPromotions.size());
                break;
            }
        }
    }

    private void callbackCancel() {
        if (itfCallbackMcp != null) {
            itfCallbackMcp.onCancelChooseMcp();
        }
//        dismiss();
    }

    private void callbackPromotionSelected() {
        if (itfCallbackMcp != null) {
            itfCallbackMcp.onPayWithMulticurrency(arrMcpList.get(0));
        }
        dismiss();
    }

    public interface ItfCallbackMcp{

        void onCancelChooseMcp();
        public void onPayWithMulticurrency(McpInfo arrMcp);
    }
}
