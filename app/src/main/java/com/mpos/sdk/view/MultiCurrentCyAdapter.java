package com.mpos.sdk.view;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.mpos.sdk.R;
import com.mpos.sdk.core.modelma.McpInfo;
import com.mpos.sdk.core.modelma.PromotionInfo;
import com.mpos.sdk.databinding.ItemMultiCurrentcyBinding;
import com.mpos.sdk.databinding.ItemPromotionBinding;
import com.mpos.sdk.util.Utils;
import com.nostra13.universalimageloader.core.ImageLoader;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;

public class MultiCurrentCyAdapter extends RecyclerView.Adapter<MultiCurrentCyAdapter.ViewHolder> {

    private static final String TAG = "PromotionAdapter";
    private final ArrayList<McpInfo> arrItemChild;
    private final Context context;
    private final IClickItemChild iClickItemChild;
    private final ArrayList<ViewHolder> arrayListViewHolder = new ArrayList<>();

    public MultiCurrentCyAdapter(Context context, ArrayList<McpInfo> arr, IClickItemChild iClickItemChild){
        super();
        this.arrItemChild       = arr;
        this.context            = context;
        this.iClickItemChild = iClickItemChild;
    }

    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType){
//        View view = LayoutInflater.from(context).inflate(R.layout.item_promotion, parent, false);

        return new ViewHolder(ItemMultiCurrentcyBinding.inflate(LayoutInflater.from(context), parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder viewHolder, int position){
        final McpInfo item = arrItemChild.get(position);

        viewHolder.binding.tvTitle.setText(item.getCurrencyCode());
        viewHolder.binding.tvSubTitle.setText(String.format("-%sđ",Utils.zenMoney(item.getAmount())));
        viewHolder.binding.tvSubTitle.setText(item.getFee());
//        ImageLoader.getInstance().displayImage(item.getLogoUrl(), viewHolder.binding.imvThumb);
//        viewHolder.binding.checkbox.setChecked(item.isChecked());
//        optional = 0; //Mặc định không chọn
//        optional = 1; //Bắt buộc chọn không được bỏ chọn
//        optional = 2; //Mặc định chọn, cho phép bỏ chọn
        viewHolder.binding.checkbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            changeBackground(viewHolder.binding.getRoot(), isChecked);
            if (iClickItemChild != null) {
                iClickItemChild.onCheckedChange(arrItemChild.get(position));
            }
        });
        changeBackground(viewHolder.binding.getRoot(), true);

        viewHolder.binding.getRoot().setOnClickListener(v -> {

            if (iClickItemChild != null) {
                iClickItemChild.clickItemChild(item);
            }
        });

        arrayListViewHolder.add(viewHolder);
    }

    private void changeBackground(View view, boolean checked) {
        view.setBackgroundResource(checked? R.drawable.bg_dash_blue_border_round:R.drawable.bg_dash_white_border_round);
    }

    @Override
    public int getItemCount(){
        int count = arrItemChild == null ? 0 : arrItemChild.size();
        Utils.LOGD(TAG, "getItemCount: "+count);
        return count;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder{

        ItemMultiCurrentcyBinding binding;

        public ViewHolder(ItemMultiCurrentcyBinding inflate){
            super(inflate.getRoot());
            this.binding = inflate;
        }
    }


    public interface IClickItemChild {
        void clickItemChild(McpInfo item);
        void onCheckedChange(McpInfo item);

    }

}
